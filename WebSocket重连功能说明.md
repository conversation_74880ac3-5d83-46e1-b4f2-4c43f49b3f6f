# WebSocket断开重连功能实现说明

## 概述

本文档说明ESP32设备中WebSocket断开重连功能的实现方案。该功能已完全集成到`sk_websocket.c`模块中，提供了内置的自动重连机制，无需在应用层额外处理。

## 功能特点

### ✅ **内置自动重连机制**

1. **WebSocket主任务内置重连**
   - 连接失败时自动重试
   - 断开后自动重连
   - 可配置的重连参数

2. **智能重连策略**
   - 最大重连次数限制（默认10次）
   - 可配置重连间隔（默认5秒）
   - 连接成功后重置计数器

3. **WiFi重连后WebSocket重连**
   - WiFi连接成功后自动检查WebSocket状态
   - 网络稳定后自动启动WebSocket连接
   - 集成到状态机的网络事件处理中

## 实现细节

### 1. WebSocket控制结构体扩展

<augment_code_snippet path="main/protocol/sk_websocket.c" mode="EXCERPT">
```c
typedef struct {
    bool connected;
    uint16_t port;
    int sock;
    EventGroupHandle_t eventGroup;
    TaskHandle_t rxTaskHandle;
    uint8_t *buffer;

    char serverIp[16];
    void *onTextFrameArg;
    void *onBinaryFrameArg;
    void *onEventArg;
    SkWsDataCallback_t onTextFrameCb;
    SkWsDataCallback_t onBinaryFrameCb;
    SkWsEventCallback_t onEventCb;

    // 自动重连相关字段
    uint32_t reconnectCount;
    uint32_t maxReconnectCount;
    uint32_t reconnectDelayMs;
    bool autoReconnectEnabled;
} SkWebsocket_t;
```
</augment_code_snippet>

### 2. 内置自动重连逻辑

<augment_code_snippet path="main/protocol/sk_websocket.c" mode="EXCERPT">
```c
// 连接失败处理
if (SkWsConnect(ctrl) != SK_RET_SUCCESS) {
    if (ctrl->autoReconnectEnabled && ctrl->reconnectCount < ctrl->maxReconnectCount) {
        ctrl->reconnectCount++;
        SK_LOGI(TAG, "WebSocket connect failed, retry %d/%d after %d ms",
               ctrl->reconnectCount, ctrl->maxReconnectCount, ctrl->reconnectDelayMs);
        event = xEventGroupWaitBits(ctrl->eventGroup, flagAtReconnect, pdFALSE, pdFALSE,
                                  pdMS_TO_TICKS(ctrl->reconnectDelayMs));
    }
    continue;
}

// 连接成功，重置重连计数器
ctrl->reconnectCount = 0;
SK_LOGI(TAG, "WebSocket connected successfully");

// 断开后自动重连
if (ctrl->autoReconnectEnabled && ctrl->reconnectCount < ctrl->maxReconnectCount) {
    ctrl->reconnectCount++;
    SK_LOGI(TAG, "WebSocket disconnected, attempting reconnect %d/%d after %d ms",
           ctrl->reconnectCount, ctrl->maxReconnectCount, ctrl->reconnectDelayMs);
    xEventGroupSetBits(ctrl->eventGroup, SK_WS_EVENT_CONNECT_BIT);
}
```
</augment_code_snippet>

### 3. 修复后的重连逻辑

<augment_code_snippet path="main/protocol/sk_websocket.c" mode="EXCERPT">
```c
// 连接失败处理 - 只在这里增加重连计数器
if (SkWsConnect(ctrl) != SK_RET_SUCCESS) {
    ctrl->reconnectCount++;

    if (ctrl->autoReconnectEnabled && ctrl->reconnectCount < ctrl->maxReconnectCount) {
        SK_LOGI(TAG, "WebSocket connect failed, retry %d/%d after %d ms",
               ctrl->reconnectCount, ctrl->maxReconnectCount, ctrl->reconnectDelayMs);
        event = xEventGroupWaitBits(ctrl->eventGroup, flagAtReconnect, pdFALSE, pdFALSE,
                                  pdMS_TO_TICKS(ctrl->reconnectDelayMs));
    } else {
        SK_LOGE(TAG, "WebSocket max reconnect attempts reached (%d/%d), giving up",
               ctrl->reconnectCount, ctrl->maxReconnectCount);
        event = xEventGroupWaitBits(ctrl->eventGroup, flagAtReconnect, pdFALSE, pdFALSE, pdMS_TO_TICKS(5000));
    }
    continue;
}

// 连接成功，重置重连计数器
ctrl->reconnectCount = 0;

// 断开后自动重连 - 不增加计数器，让下次连接失败时再增加
// 重连计数器将在下次连接失败时增加
```
</augment_code_snippet>

### 4. 配置函数

<augment_code_snippet path="main/protocol/sk_websocket.c" mode="EXCERPT">
```c
void SkWsSetAutoReconnect(bool enabled);
void SkWsSetReconnectParams(uint32_t maxCount, uint32_t delayMs);
uint32_t SkWsGetReconnectCount();
void SkWsResetReconnectCount();  // 新增：重置重连计数器

// 手动启动连接时重置重连计数器
void SkWsStartConnect() {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    ctrl->reconnectCount = 0;  // 给予新的重连机会
    xEventGroupClearBits(ctrl->eventGroup, SK_WS_EVENT_DISCONNECT_BIT);
    xEventGroupSetBits(ctrl->eventGroup, SK_WS_EVENT_CONNECT_BIT);
}
```
</augment_code_snippet>

### 5. 重连逻辑修复说明

#### 🔧 **修复的问题**
1. **重复计数问题**：原来在连接失败和断开后都会增加计数器，导致计数器增长过快
2. **重连次数限制失效**：由于重复计数，实际重连次数比预期少一半
3. **手动重连无效**：达到最大重连次数后，手动重连也无法工作

#### ✅ **修复后的逻辑**
1. **单点计数**：只在连接失败时增加重连计数器
2. **断开重连**：断开后直接进入重连循环，不增加计数器
3. **手动重置**：`SkWsStartConnect()`会重置计数器，给予新的重连机会
4. **状态清晰**：连接成功时重置计数器，失败时增加计数器

#### 📊 **重连流程**
```
启动连接 -> 连接失败 -> 计数器+1 -> 检查是否超限 -> 延迟重试
    ↑                                                    ↓
连接成功 <- 重置计数器 <- 连接成功 <- 尝试连接 <- 等待延迟结束
    ↓
进入接收循环 -> 连接断开 -> 检查自动重连 -> 继续重连循环
```

## API接口

### 配置函数
```c
// 启用/禁用自动重连
void SkWsSetAutoReconnect(bool enabled);

// 设置重连参数
void SkWsSetReconnectParams(uint32_t maxCount, uint32_t delayMs);

// 获取当前重连次数
uint32_t SkWsGetReconnectCount();
```

### 使用示例
```c
// 在初始化时配置重连参数
SkWsSetReconnectParams(15, 3000);  // 最大15次，间隔3秒
SkWsSetAutoReconnect(true);        // 启用自动重连

// 检查重连状态
uint32_t count = SkWsGetReconnectCount();
SK_LOGI(TAG, "Current reconnect count: %d", count);
```

## 重连参数配置

### 默认参数
- **自动重连**: 启用
- **最大重连次数**: 10次
- **重连间隔**: 5000ms (5秒)
- **网络稳定等待时间**: 2000ms (2秒)

### 可配置参数
- **最大重连次数**: 通过`SkWsSetReconnectParams()`设置
- **重连间隔**: 通过`SkWsSetReconnectParams()`设置
- **自动重连开关**: 通过`SkWsSetAutoReconnect()`控制

## 重连触发条件

### 1. 自动重连触发
- WebSocket连接断开事件
- WebSocket连接失败
- WiFi重连成功后

### 2. 手动重连触发
- 调用`SkWsStartConnect()`函数
- JSON命令`start_audio`
- 系统初始化后自动连接

## 重连状态管理

### 连接状态检查
```c
bool SkWsIsConnected() {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    return ctrl->connected;
}
```

### 事件标志管理
- `SK_WS_EVENT_CONNECT_BIT`: 连接请求标志
- `SK_WS_EVENT_DISCONNECT_BIT`: 断开请求标志
- `SK_WS_EVENT_STOP_BIT`: 停止标志

## 日志输出

### 连接成功日志
```
I (12345) SmartKid: WebSocket connected successfully
```

### 重连日志
```
I (12346) SmartKid: WebSocket disconnected, attempting reconnect...
I (12347) SmartKid: WebSocket reconnect attempt 1/10
```

### WiFi重连后检查日志
```
I (12348) SmartKid: WiFi connected, checking WebSocket connection...
I (12349) SmartKid: WebSocket not connected, starting connection...
```

## 错误处理

### 重连失败处理
- 达到最大重连次数后停止重连
- 记录错误日志
- 保持系统其他功能正常运行

### 网络异常处理
- WiFi断开时WebSocket自动断开
- WiFi重连成功后WebSocket自动重连
- 网络不稳定时的重连保护

## 性能优化

### 1. 重连频率控制
- 避免频繁重连消耗资源
- 合理的重连间隔设置
- 最大重连次数限制

### 2. 内存管理
- 重连过程中的内存释放
- 避免内存泄漏
- 资源清理机制

### 3. 任务调度
- 重连任务优先级设置
- 避免阻塞其他重要任务
- 合理的延迟设置

## 测试验证

### 1. 断网重连测试
- 手动断开网络连接
- 观察自动重连行为
- 验证重连成功率

### 2. 服务器重启测试
- 重启WebSocket服务器
- 验证客户端自动重连
- 检查数据传输恢复

### 3. 长时间稳定性测试
- 24小时连续运行测试
- 网络波动环境测试
- 内存泄漏检查

## 故障排除

### 常见问题

#### 1. 重连失败
- 检查网络连接状态
- 确认服务器地址和端口
- 查看重连计数器状态

#### 2. 频繁重连
- 检查网络稳定性
- 调整重连间隔参数
- 检查服务器负载

#### 3. WiFi重连后WebSocket未重连
- 检查WiFi事件回调注册
- 确认网络稳定等待时间
- 查看相关日志输出

### 调试方法

#### 启用详细日志
```c
// 在sk_websocket.c中启用调试日志
#define LOG_LOCAL_LEVEL ESP_LOG_DEBUG
```

#### 监控重连状态
```c
// 添加状态监控代码
SK_LOGI(TAG, "WebSocket state: connected=%d, reconnectCount=%d", 
        SkWsIsConnected(), reconnectCount);
```

## 代码架构优势

### ✅ **模块化设计**
- 重连逻辑完全集成在`sk_websocket.c`中
- 应用层无需关心重连细节
- 保持代码的功能性和内聚性

### ✅ **配置灵活性**
- 支持运行时配置重连参数
- 可以动态启用/禁用自动重连
- 提供状态查询接口

### ✅ **系统集成**
- 与WiFi状态机无缝集成
- 网络重连后自动恢复WebSocket连接
- 不影响现有的应用逻辑

## 总结

本WebSocket重连功能实现了完整的内置自动重连机制，具有以下特点：

✅ **内置重连逻辑**：完全集成在WebSocket模块中，无需外部处理
✅ **智能重连策略**：可配置的重连次数和间隔，连接成功后重置计数器
✅ **WiFi集成**：WiFi重连成功后自动检查并重连WebSocket
✅ **灵活配置**：提供API接口动态配置重连参数
✅ **模块化设计**：保持代码的功能性和可维护性

该实现遵循了良好的软件设计原则，确保了WebSocket连接的高可用性和系统的稳定运行。
