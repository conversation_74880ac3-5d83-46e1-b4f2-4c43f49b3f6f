#!/usr/bin/env python3
"""
SK音频协议处理模块
基于websocket_audio_server.py的协议实现
"""

import struct
import time
import json
import numpy as np
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SkAudioProtocol:
    """SK音频协议处理类"""
    
    def __init__(self):
        self.seqNum = 0
        
    def CreateAudioPacket(self, opusData: bytes) -> bytes:
        """创建WebSocket音频包"""
        version = 0x01
        audioType = 0x01
        payloadLen = len(opusData)
        reserved = 0x00
        
        # 打包头部（小端序，与ESP32一致）
        header = struct.pack('<BBHHH', 
                           version,        # version
                           audioType,     # type
                           self.seqNum & 0xFFFF,  # seqNum
                           payloadLen,    # payloadLen
                           reserved)       # resv
        
        self.seqNum += 1
        return header + opusData
    
    def ParseAudioPacket(self, data: bytes) -> dict:
        """解析音频包"""
        if len(data) < 8:
            return None
            
        try:
            header = struct.unpack('<BBHHH', data[:8])
            return {
                'version': header[0],
                'type': header[1],
                'seqNum': header[2],
                'payloadLen': header[3],
                'reserved': header[4],
                'payload': data[8:]
            }
        except Exception as e:
            logger.error(f"Failed to parse audio packet: {e}")
            return None
    
    def CreateJsonMessage(self, msgType: str, data: dict = None, seq: int = None) -> str:
        """创建JSON消息"""
        if seq is None:
            seq = int(time.time())
            
        message = {
            "type": msgType,
            "timestamp": int(time.time()),
            "seq": seq
        }
        
        if data:
            message["data"] = data
            
        return json.dumps(message)
    
    def ParseJsonMessage(self, jsonStr: str) -> dict:
        """解析JSON消息"""
        try:
            return json.loads(jsonStr)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON: {e}")
            return None

class SkAudioProcessor:
    """音频处理类"""
    
    def __init__(self):
        self.opusEncoder = None
        self.InitOpusEncoder()
        
    def InitOpusEncoder(self):
        """初始化Opus编码器"""
        try:
            import opuslib
            # 16kHz采样率，单声道，VoIP应用
            self.opusEncoder = opuslib.Encoder(16000, 1, opuslib.APPLICATION_VOIP)
            logger.info("✓ Opus编码器初始化成功 (16kHz, 单声道)")
        except ImportError:
            logger.warning("⚠ Opus编码器不可用，请安装: pip install opuslib")
            self.opusEncoder = None
        except Exception as e:
            logger.error(f"✗ Opus编码器初始化失败: {e}")
            self.opusEncoder = None
    
    def LoadWavFile(self, wavFile: str) -> dict:
        """加载WAV文件"""
        try:
            import wave
            
            with wave.open(wavFile, 'rb') as wav:
                sampleRate = wav.getframerate()
                channels = wav.getnchannels()
                sampleWidth = wav.getsampwidth()
                frames = wav.readframes(wav.getnframes())
                
                logger.info(f"WAV文件信息: {wavFile}")
                logger.info(f"  采样率: {sampleRate} Hz")
                logger.info(f"  声道数: {channels}")
                logger.info(f"  位深: {sampleWidth * 8} bit")
                
                # 转换为numpy数组
                if sampleWidth == 1:
                    dtype = np.uint8
                elif sampleWidth == 2:
                    dtype = np.int16
                else:
                    dtype = np.int32
                
                audioData = np.frombuffer(frames, dtype=dtype)
                
                # 如果是立体声，转换为单声道
                if channels == 2:
                    audioData = audioData.reshape(-1, 2)
                    audioData = np.mean(audioData, axis=1).astype(dtype)
                
                # 重采样到16kHz（如果需要）
                if sampleRate != 16000:
                    audioData = self.ResampleAudio(audioData, sampleRate, 16000)
                
                duration = len(audioData) / 16000
                logger.info(f"  处理后: 16000 Hz, 单声道, {len(audioData)} 采样点")
                logger.info(f"  时长: {duration:.2f} 秒")
                
                return {
                    'file': wavFile,
                    'data': audioData,
                    'duration': duration,
                    'pos': 0
                }
                
        except Exception as e:
            logger.error(f"加载WAV文件失败: {wavFile} - {e}")
            return None
    
    def ResampleAudio(self, audioData: np.ndarray, origSr: int, targetSr: int) -> np.ndarray:
        """简单的重采样（线性插值）"""
        if origSr == targetSr:
            return audioData
        
        # 计算重采样比例
        ratio = targetSr / origSr
        newLength = int(len(audioData) * ratio)
        
        # 线性插值重采样
        oldIndices = np.linspace(0, len(audioData) - 1, newLength)
        newAudio = np.interp(oldIndices, np.arange(len(audioData)), audioData)
        
        return newAudio.astype(audioData.dtype)
    
    def EncodeToOpus(self, pcmFrame: np.ndarray) -> bytes:
        """使用Opus编码器编码PCM数据"""
        if not self.opusEncoder:
            return self.EncodeToPseudoOpus(pcmFrame.tobytes())
        
        try:
            # 确保输入是320个样本的int16数组
            if len(pcmFrame) != 320:
                logger.warning(f"帧大小不正确 {len(pcmFrame)}, 期望320")
                return None
            
            # Opus编码 - 输入必须是bytes格式
            pcmBytes = pcmFrame.astype(np.int16).tobytes()
            opusData = self.opusEncoder.encode(pcmBytes, 320)
            
            if len(opusData) > 0:
                return opusData
            else:
                logger.warning("Opus编码返回空数据")
                return None
                
        except Exception as e:
            logger.error(f"Opus编码失败: {e}")
            return None
    
    def EncodeToPseudoOpus(self, pcmData: bytes) -> bytes:
        """备用的伪Opus编码（当真正的Opus不可用时）"""
        # 添加伪Opus头部标识
        opusHeader = b'\xFC\x00'  # 伪Opus标识
        
        # 保持完整的PCM数据，不要截断
        maxPayload = min(len(pcmData), 640)  # 最大640字节（320样本*2字节）
        
        return opusHeader + pcmData[:maxPayload]
    
    def GetAudioFrame(self, audioData: np.ndarray, pos: int, frameSize: int = 320) -> tuple:
        """获取一帧音频数据"""
        if pos + frameSize >= len(audioData):
            return None, pos  # 音频结束
        
        frame = audioData[pos:pos + frameSize]
        
        # 确保帧大小正确
        if len(frame) < frameSize:
            # 如果不足一帧，用零填充
            paddedFrame = np.zeros(frameSize, dtype=np.int16)
            paddedFrame[:len(frame)] = frame.astype(np.int16)
            frame = paddedFrame
        else:
            frame = frame.astype(np.int16)
        
        return frame, pos + frameSize
    
    def GenerateSimulatedOpusData(self, seqNum: int) -> bytes:
        """生成模拟的Opus数据（当没有WAV文件时使用）"""
        if self.opusEncoder:
            # 生成静音PCM数据（320个样本）
            silenceFrame = np.zeros(320, dtype=np.int16)
            
            # 添加一些轻微的噪声，避免完全静音
            noise = np.random.randint(-100, 100, 320, dtype=np.int16)
            silenceFrame = silenceFrame + noise
            
            # 使用真正的Opus编码
            return self.EncodeToOpus(silenceFrame)
        else:
            # 备用：生成伪Opus数据
            frameSize = 60  # 固定大小
            opusData = bytearray(frameSize)
            
            if frameSize > 0:
                opusData[0] = 0xFC  # Opus帧开始标志（模拟）
            if frameSize > 1:
                opusData[1] = frameSize & 0xFF
            
            for i in range(2, frameSize):
                opusData[i] = (seqNum + i) & 0xFF
            
            return bytes(opusData)

class SkMessageHandler:
    """消息处理类"""
    
    def __init__(self):
        self.protocol = SkAudioProtocol()
        
    def CreateWelcomeMessage(self, clientId: int, connectedClients: int) -> str:
        """创建欢迎消息"""
        data = {
            "event": "connected",
            "message": "Welcome to SK Audio WebSocket Server",
            "server_time": time.strftime('%Y-%m-%d %H:%M:%S'),
            "client_id": clientId,
            "connected_clients": connectedClients
        }
        return self.protocol.CreateJsonMessage("notification", data)
    
    def CreateStatusResponse(self, seq: int, serverStatus: dict) -> str:
        """创建状态响应消息"""
        return self.protocol.CreateJsonMessage("response", {
            "seq": seq,
            "result": "success",
            "message": "Status retrieved successfully",
            "data": serverStatus
        })
    
    def CreateCommandResponse(self, seq: int, cmd: str, result: str, message: str, data: dict = None) -> str:
        """创建命令响应消息"""
        responseData = {
            "seq": seq,
            "result": result,
            "message": message,
            "command": cmd,
            "processed_at": time.strftime('%Y-%m-%d %H:%M:%S')
        }
        if data:
            responseData.update(data)
            
        return self.protocol.CreateJsonMessage("response", responseData)
    
    def ProcessCommand(self, data: dict) -> dict:
        """处理命令消息"""
        cmdData = data.get('data', {})
        cmd = cmdData.get('cmd', '')
        seq = data.get('seq', 0)
        
        result = "success"
        message = f"Command '{cmd}' processed successfully"
        responseData = {}
        
        if cmd == "start_audio":
            message = "Audio streaming started"
        elif cmd == "stop_audio":
            message = "Audio streaming stopped"
        elif cmd == "reload_audio":
            message = "Audio files reloaded"
        elif cmd == "get_status":
            message = "Status retrieved"
        else:
            result = "error"
            message = f"Unknown command: {cmd}"
        
        return {
            "seq": seq,
            "cmd": cmd,
            "result": result,
            "message": message,
            "data": responseData
        }
