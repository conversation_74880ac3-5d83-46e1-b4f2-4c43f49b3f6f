#!/usr/bin/env python3
"""
WebSocket音频控制服务器
支持JSON命令控制和音频流传输
作者: AI Assistant
日期: 2025-01-01
"""

import asyncio
import websockets
import json
import time
import threading
import struct
import wave
import numpy as np
import os
import sys
from pathlib import Path

class AudioControlServer:
    def __init__(self, host='0.0.0.0', port=8768):
        self.host = host
        self.port = port
        self.clients = set()
        self.audio_streaming = False
        self.audio_thread = None
        self.audio_data = None
        self.audio_pos = 0
        self.sample_rate = 16000
        self.frame_size = 320  # 20ms at 16kHz
        self.seq_num = 0
        
        # 音频文件路径
        self.audio_files = []
        self.current_file_index = 0
        
        print(f"[{time.strftime('%H:%M:%S')}] 音频控制服务器初始化完成")
        print(f"[{time.strftime('%H:%M:%S')}] 服务器地址: {host}:{port}")
        
    async def register_client(self, websocket):
        """注册新客户端"""
        self.clients.add(websocket)
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        print(f"[{time.strftime('%H:%M:%S')}] 客户端连接: {client_info}")
        print(f"[{time.strftime('%H:%M:%S')}] 当前连接数: {len(self.clients)}")
        
    async def unregister_client(self, websocket):
        """注销客户端"""
        self.clients.discard(websocket)
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        print(f"[{time.strftime('%H:%M:%S')}] 客户端断开: {client_info}")
        print(f"[{time.strftime('%H:%M:%S')}] 当前连接数: {len(self.clients)}")
        
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        await self.register_client(websocket)
        try:
            async for message in websocket:
                if isinstance(message, str):
                    # 处理JSON文本消息
                    await self.handle_json_message(websocket, message)
                else:
                    # 处理二进制消息
                    await self.handle_binary_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            pass
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 客户端处理错误: {e}")
        finally:
            await self.unregister_client(websocket)
            
    async def handle_json_message(self, websocket, message):
        """处理JSON消息"""
        try:
            data = json.loads(message)
            print(f"[{time.strftime('%H:%M:%S')}] 收到JSON消息: {data}")
            
            msg_type = data.get('type', '')
            if msg_type == 'command':
                await self.handle_command(websocket, data)
            elif msg_type == 'config':
                await self.handle_config(websocket, data)
            else:
                print(f"[{time.strftime('%H:%M:%S')}] 未知消息类型: {msg_type}")
                
        except json.JSONDecodeError as e:
            print(f"[{time.strftime('%H:%M:%S')}] JSON解析错误: {e}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 处理JSON消息错误: {e}")
            
    async def handle_command(self, websocket, data):
        """处理命令消息"""
        cmd_data = data.get('data', {})
        cmd = cmd_data.get('cmd', '')
        
        print(f"[{time.strftime('%H:%M:%S')}] 处理命令: {cmd}")
        
        if cmd == 'start_audio':
            await self.start_audio_streaming()
        elif cmd == 'stop_audio':
            await self.stop_audio_streaming()
        elif cmd == 'get_status':
            await self.send_status(websocket)
        elif cmd == 'load_audio':
            file_path = cmd_data.get('file_path', '')
            await self.load_audio_file(file_path)
        else:
            print(f"[{time.strftime('%H:%M:%S')}] 未知命令: {cmd}")
            
    async def handle_config(self, websocket, data):
        """处理配置消息"""
        config_data = data.get('data', {})
        print(f"[{time.strftime('%H:%M:%S')}] 处理配置: {config_data}")
        
        # 更新配置参数
        if 'sample_rate' in config_data:
            self.sample_rate = config_data['sample_rate']
            print(f"[{time.strftime('%H:%M:%S')}] 采样率设置为: {self.sample_rate}")
            
        if 'frame_size' in config_data:
            self.frame_size = config_data['frame_size']
            print(f"[{time.strftime('%H:%M:%S')}] 帧大小设置为: {self.frame_size}")
            
    async def handle_binary_message(self, websocket, message):
        """处理二进制消息"""
        print(f"[{time.strftime('%H:%M:%S')}] 收到二进制消息，长度: {len(message)} 字节")
        
    async def start_audio_streaming(self):
        """开始音频流传输"""
        if self.audio_streaming:
            print(f"[{time.strftime('%H:%M:%S')}] 音频流已经在运行中")
            return
            
        if not self.audio_data:
            print(f"[{time.strftime('%H:%M:%S')}] 没有加载音频数据，尝试加载默认音频")
            await self.load_default_audio()
            
        if not self.audio_data:
            print(f"[{time.strftime('%H:%M:%S')}] 无法加载音频数据，使用模拟数据")
            self.generate_test_audio()
            
        self.audio_streaming = True
        self.audio_pos = 0
        self.seq_num = 0
        
        # 启动音频传输线程
        if self.audio_thread is None or not self.audio_thread.is_alive():
            self.audio_thread = threading.Thread(target=self.audio_streaming_worker)
            self.audio_thread.daemon = True
            self.audio_thread.start()
            
        print(f"[{time.strftime('%H:%M:%S')}] 音频流传输已启动")
        
    async def stop_audio_streaming(self):
        """停止音频流传输"""
        if not self.audio_streaming:
            print(f"[{time.strftime('%H:%M:%S')}] 音频流已经停止")
            return
            
        self.audio_streaming = False
        print(f"[{time.strftime('%H:%M:%S')}] 音频流传输已停止")
        
    def audio_streaming_worker(self):
        """音频流传输工作线程"""
        print(f"[{time.strftime('%H:%M:%S')}] 音频传输线程启动")
        
        while self.audio_streaming:
            if not self.clients:
                time.sleep(0.1)
                continue
                
            # 获取音频帧数据
            frame_data = self.get_next_audio_frame()
            if frame_data is None:
                # 音频播放完毕，重新开始
                self.audio_pos = 0
                continue
                
            # 创建WebSocket二进制帧
            binary_frame = self.create_binary_frame(frame_data)
            
            # 发送给所有客户端
            asyncio.run_coroutine_threadsafe(
                self.broadcast_binary_data(binary_frame),
                asyncio.get_event_loop()
            )
            
            # 控制发送频率 (20ms间隔)
            time.sleep(0.02)
            
        print(f"[{time.strftime('%H:%M:%S')}] 音频传输线程结束")
        
    def get_next_audio_frame(self):
        """获取下一个音频帧"""
        if not self.audio_data or self.audio_pos >= len(self.audio_data):
            return None
            
        frame_end = min(self.audio_pos + self.frame_size, len(self.audio_data))
        frame_data = self.audio_data[self.audio_pos:frame_end]
        self.audio_pos = frame_end
        
        # 如果帧不够长，用零填充
        if len(frame_data) < self.frame_size:
            frame_data = np.pad(frame_data, (0, self.frame_size - len(frame_data)), 'constant')
            
        return frame_data.astype(np.int16).tobytes()
        
    def create_binary_frame(self, audio_data):
        """创建WebSocket二进制帧"""
        # WebSocket二进制帧头部结构
        # version(1) + type(1) + seqNum(2) + payloadLen(2) + resv(2) + data
        version = 1
        data_type = 1  # 音频数据类型
        payload_len = len(audio_data)
        resv = 0
        
        header = struct.pack('<BBHHH', version, data_type, self.seq_num, payload_len, resv)
        self.seq_num = (self.seq_num + 1) % 65536
        
        return header + audio_data
        
    async def broadcast_binary_data(self, data):
        """广播二进制数据给所有客户端"""
        if not self.clients:
            return
            
        disconnected_clients = set()
        for client in self.clients:
            try:
                await client.send(data)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                print(f"[{time.strftime('%H:%M:%S')}] 发送数据错误: {e}")
                disconnected_clients.add(client)
                
        # 移除断开的客户端
        for client in disconnected_clients:
            self.clients.discard(client)
            
    async def send_status(self, websocket):
        """发送状态信息"""
        status = {
            "type": "status",
            "timestamp": int(time.time()),
            "data": {
                "audio_streaming": self.audio_streaming,
                "connected_clients": len(self.clients),
                "sample_rate": self.sample_rate,
                "frame_size": self.frame_size,
                "seq_num": self.seq_num,
                "audio_loaded": self.audio_data is not None,
                "audio_length": len(self.audio_data) if self.audio_data is not None else 0
            }
        }
        
        try:
            await websocket.send(json.dumps(status))
            print(f"[{time.strftime('%H:%M:%S')}] 状态信息已发送")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 发送状态错误: {e}")

    async def load_audio_file(self, file_path):
        """加载音频文件"""
        if not file_path or not os.path.exists(file_path):
            print(f"[{time.strftime('%H:%M:%S')}] 音频文件不存在: {file_path}")
            return False

        try:
            with wave.open(file_path, 'rb') as wav_file:
                frames = wav_file.readframes(-1)
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()

                # 转换为numpy数组
                if sample_width == 1:
                    audio_data = np.frombuffer(frames, dtype=np.uint8)
                    audio_data = (audio_data.astype(np.float32) - 128) / 128.0
                elif sample_width == 2:
                    audio_data = np.frombuffer(frames, dtype=np.int16)
                    audio_data = audio_data.astype(np.float32) / 32768.0
                else:
                    print(f"[{time.strftime('%H:%M:%S')}] 不支持的采样位深: {sample_width}")
                    return False

                # 处理多声道
                if channels > 1:
                    audio_data = audio_data.reshape(-1, channels)
                    audio_data = np.mean(audio_data, axis=1)  # 转为单声道

                # 重采样到目标采样率
                if sample_rate != self.sample_rate:
                    audio_data = self.resample_audio(audio_data, sample_rate, self.sample_rate)

                # 转换为int16格式
                self.audio_data = (audio_data * 32767).astype(np.int16)

                print(f"[{time.strftime('%H:%M:%S')}] 音频文件加载成功: {file_path}")
                print(f"[{time.strftime('%H:%M:%S')}] 采样率: {sample_rate} -> {self.sample_rate}")
                print(f"[{time.strftime('%H:%M:%S')}] 声道数: {channels} -> 1")
                print(f"[{time.strftime('%H:%M:%S')}] 音频长度: {len(self.audio_data)} 采样点")
                return True

        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 加载音频文件错误: {e}")
            return False

    async def load_default_audio(self):
        """加载默认音频文件"""
        # 查找audio文件夹中的音频文件
        audio_dir = Path("audio")
        if audio_dir.exists():
            audio_files = list(audio_dir.glob("*.wav"))
            if audio_files:
                return await self.load_audio_file(str(audio_files[0]))

        # 查找当前目录中的音频文件
        current_dir = Path(".")
        audio_files = list(current_dir.glob("*.wav"))
        if audio_files:
            return await self.load_audio_file(str(audio_files[0]))

        return False

    def generate_test_audio(self):
        """生成测试音频数据"""
        duration = 5.0  # 5秒
        samples = int(duration * self.sample_rate)

        # 生成1kHz正弦波
        t = np.linspace(0, duration, samples, False)
        frequency = 1000  # 1kHz
        audio_data = np.sin(2 * np.pi * frequency * t)

        # 添加包络以避免突然开始/结束
        fade_samples = int(0.1 * self.sample_rate)  # 100ms淡入淡出
        fade_in = np.linspace(0, 1, fade_samples)
        fade_out = np.linspace(1, 0, fade_samples)

        audio_data[:fade_samples] *= fade_in
        audio_data[-fade_samples:] *= fade_out

        # 转换为int16格式
        self.audio_data = (audio_data * 32767 * 0.5).astype(np.int16)

        print(f"[{time.strftime('%H:%M:%S')}] 生成测试音频: {duration}秒, {frequency}Hz正弦波")

    def resample_audio(self, audio_data, old_rate, new_rate):
        """重采样音频数据"""
        if old_rate == new_rate:
            return audio_data

        # 简单的线性插值重采样
        old_length = len(audio_data)
        new_length = int(old_length * new_rate / old_rate)

        old_indices = np.linspace(0, old_length - 1, new_length)
        new_audio = np.interp(old_indices, np.arange(old_length), audio_data)

        return new_audio

    async def broadcast_json_message(self, message):
        """广播JSON消息给所有客户端"""
        if not self.clients:
            return

        json_str = json.dumps(message)
        disconnected_clients = set()

        for client in self.clients:
            try:
                await client.send(json_str)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                print(f"[{time.strftime('%H:%M:%S')}] 发送JSON消息错误: {e}")
                disconnected_clients.add(client)

        # 移除断开的客户端
        for client in disconnected_clients:
            self.clients.discard(client)

    def start_server(self):
        """启动服务器"""
        print(f"[{time.strftime('%H:%M:%S')}] 启动WebSocket服务器...")
        print(f"[{time.strftime('%H:%M:%S')}] 监听地址: ws://{self.host}:{self.port}")

        start_server = websockets.serve(self.handle_client, self.host, self.port)

        # 启动命令行界面
        threading.Thread(target=self.command_interface, daemon=True).start()

        # 运行服务器
        asyncio.get_event_loop().run_until_complete(start_server)
        asyncio.get_event_loop().run_forever()

    def command_interface(self):
        """命令行界面"""
        print(f"\n[{time.strftime('%H:%M:%S')}] 服务器控制台已启动")
        print("可用命令:")
        print("  start    - 开始音频流传输")
        print("  stop     - 停止音频流传输")
        print("  status   - 显示服务器状态")
        print("  load <file> - 加载音频文件")
        print("  test     - 发送测试JSON消息")
        print("  clients  - 显示连接的客户端")
        print("  help     - 显示帮助")
        print("  quit     - 退出服务器")
        print()

        while True:
            try:
                command = input("> ").strip().lower()

                if command == "start":
                    asyncio.run_coroutine_threadsafe(
                        self.start_audio_streaming(),
                        asyncio.get_event_loop()
                    )
                elif command == "stop":
                    asyncio.run_coroutine_threadsafe(
                        self.stop_audio_streaming(),
                        asyncio.get_event_loop()
                    )
                elif command == "status":
                    self.show_status()
                elif command.startswith("load "):
                    file_path = command[5:].strip()
                    asyncio.run_coroutine_threadsafe(
                        self.load_audio_file(file_path),
                        asyncio.get_event_loop()
                    )
                elif command == "test":
                    asyncio.run_coroutine_threadsafe(
                        self.send_test_commands(),
                        asyncio.get_event_loop()
                    )
                elif command == "clients":
                    self.show_clients()
                elif command == "help":
                    self.show_help()
                elif command == "quit":
                    print(f"[{time.strftime('%H:%M:%S')}] 服务器正在关闭...")
                    os._exit(0)
                else:
                    print(f"未知命令: {command}，输入 'help' 查看帮助")

            except KeyboardInterrupt:
                print(f"\n[{time.strftime('%H:%M:%S')}] 服务器正在关闭...")
                os._exit(0)
            except Exception as e:
                print(f"[{time.strftime('%H:%M:%S')}] 命令处理错误: {e}")

    def show_status(self):
        """显示服务器状态"""
        print(f"\n=== 服务器状态 ===")
        print(f"服务器地址: {self.host}:{self.port}")
        print(f"连接客户端数: {len(self.clients)}")
        print(f"音频流状态: {'运行中' if self.audio_streaming else '已停止'}")
        print(f"采样率: {self.sample_rate} Hz")
        print(f"帧大小: {self.frame_size} 采样点")
        print(f"序列号: {self.seq_num}")
        print(f"音频数据: {'已加载' if self.audio_data is not None else '未加载'}")
        if self.audio_data is not None:
            duration = len(self.audio_data) / self.sample_rate
            print(f"音频长度: {len(self.audio_data)} 采样点 ({duration:.2f}秒)")
        print()

    def show_clients(self):
        """显示连接的客户端"""
        print(f"\n=== 连接的客户端 ({len(self.clients)}) ===")
        for i, client in enumerate(self.clients, 1):
            print(f"{i}. {client.remote_address[0]}:{client.remote_address[1]}")
        print()

    def show_help(self):
        """显示帮助信息"""
        print(f"\n=== 帮助信息 ===")
        print("start         - 开始向所有客户端发送音频流")
        print("stop          - 停止音频流传输")
        print("status        - 显示服务器当前状态")
        print("load <file>   - 加载指定的WAV音频文件")
        print("test          - 向所有客户端发送测试JSON命令")
        print("clients       - 显示当前连接的客户端列表")
        print("help          - 显示此帮助信息")
        print("quit          - 关闭服务器并退出")
        print()

    async def send_test_commands(self):
        """发送测试命令"""
        print(f"[{time.strftime('%H:%M:%S')}] 发送测试命令...")

        # 发送start_audio命令
        start_cmd = {
            "type": "command",
            "seq": 1001,
            "timestamp": int(time.time()),
            "data": {
                "cmd": "start_audio"
            }
        }
        await self.broadcast_json_message(start_cmd)
        print(f"[{time.strftime('%H:%M:%S')}] 已发送 start_audio 命令")

        await asyncio.sleep(2)

        # 发送get_status命令
        status_cmd = {
            "type": "command",
            "seq": 1002,
            "timestamp": int(time.time()),
            "data": {
                "cmd": "get_status"
            }
        }
        await self.broadcast_json_message(status_cmd)
        print(f"[{time.strftime('%H:%M:%S')}] 已发送 get_status 命令")

        await asyncio.sleep(3)

        # 发送stop_audio命令
        stop_cmd = {
            "type": "command",
            "seq": 1003,
            "timestamp": int(time.time()),
            "data": {
                "cmd": "stop_audio"
            }
        }
        await self.broadcast_json_message(stop_cmd)
        print(f"[{time.strftime('%H:%M:%S')}] 已发送 stop_audio 命令")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='WebSocket音频控制服务器')
    parser.add_argument('--host', default='0.0.0.0', help='服务器监听地址')
    parser.add_argument('--port', type=int, default=8768, help='服务器监听端口')
    parser.add_argument('--audio', help='启动时加载的音频文件路径')

    args = parser.parse_args()

    # 创建服务器实例
    server = AudioControlServer(args.host, args.port)

    # 如果指定了音频文件，预加载
    if args.audio:
        asyncio.run(server.load_audio_file(args.audio))

    try:
        # 启动服务器
        server.start_server()
    except KeyboardInterrupt:
        print(f"\n[{time.strftime('%H:%M:%S')}] 服务器已关闭")
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 服务器错误: {e}")


if __name__ == "__main__":
    main()
