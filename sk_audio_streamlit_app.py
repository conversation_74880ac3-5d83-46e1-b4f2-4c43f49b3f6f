#!/usr/bin/env python3
"""
SK音频WebSocket服务器 - Streamlit控制界面
基于websocket_audio_server.py的Web界面版本
"""

import streamlit as st
import asyncio
import threading
import time
import json
import os
import glob
from sk_audio_websocket_server import SkAudioWebSocketServer, GetServerInstance
import logging

# 配置页面
st.set_page_config(
    page_title="SK Audio WebSocket Server",
    page_icon="🎵",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化会话状态
if 'server' not in st.session_state:
    st.session_state.server = None
if 'server_thread' not in st.session_state:
    st.session_state.server_thread = None
if 'server_running' not in st.session_state:
    st.session_state.server_running = False
if 'server_logs' not in st.session_state:
    st.session_state.server_logs = []
if 'audio_files' not in st.session_state:
    st.session_state.audio_files = []
if 'message_history' not in st.session_state:
    st.session_state.message_history = []
if 'audio_playing' not in st.session_state:
    st.session_state.audio_playing = False

def StartServerThread(host: str, port: int):
    """在后台线程启动服务器"""
    def run_server():
        try:
            server = SkAudioWebSocketServer(host, port)
            st.session_state.server = server
            asyncio.run(server.Start())
        except Exception as e:
            logger.error(f"Server error: {e}")
            st.session_state.server_running = False
    
    if not st.session_state.server_running:
        st.session_state.server_running = True
        thread = threading.Thread(target=run_server, daemon=True)
        thread.start()
        st.session_state.server_thread = thread
        return True
    return False

def StopServer():
    """停止服务器"""
    if st.session_state.server:
        st.session_state.server.isPlaying = False
        st.session_state.server_running = False
        st.session_state.server = None

def GetAudioFiles():
    """获取音频文件列表"""
    audioDir = "audio"
    if not os.path.exists(audioDir):
        return []
    
    audioExtensions = ['*.wav', '*.WAV']
    audioFiles = []
    
    for extension in audioExtensions:
        pattern = os.path.join(audioDir, extension)
        audioFiles.extend(glob.glob(pattern))
    
    return audioFiles

def FormatFileSize(size: int) -> str:
    """格式化文件大小"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} TB"

def SendJsonMessage(server, messageType: str, data: dict = None):
    """发送JSON消息到所有连接的客户端"""
    if not server or not server.connectedClients:
        st.warning("没有连接的客户端")
        return False

    try:
        message = {
            "type": messageType,
            "timestamp": int(time.time()),
            "data": data or {}
        }

        # 添加到消息历史
        st.session_state.message_history.append({
            "time": time.strftime('%H:%M:%S'),
            "type": "发送",
            "content": json.dumps(message, ensure_ascii=False, indent=2)
        })

        # 这里应该通过服务器发送消息，但由于架构限制，我们只记录消息
        st.success(f"JSON消息已准备发送: {messageType}")
        return True

    except Exception as e:
        st.error(f"发送消息失败: {e}")
        return False

# 主界面
st.title("🎵 SK Audio WebSocket Server")
st.markdown("**基于websocket_audio_server.py的Streamlit控制界面**")

# 状态指示器
col1, col2, col3 = st.columns(3)
with col1:
    if st.session_state.server_running:
        st.success("🟢 服务器运行中")
    else:
        st.error("🔴 服务器未运行")

with col2:
    if st.session_state.audio_playing:
        st.success("🎵 音频播放中")
    else:
        st.info("⏸️ 音频已停止")

with col3:
    if st.session_state.server and st.session_state.server.connectedClients:
        client_count = len([ws for ws in st.session_state.server.connectedClients if not ws.closed])
        st.info(f"👥 {client_count} 个客户端连接")
    else:
        st.info("👥 无客户端连接")

# 主控制面板
st.header("🎛️ 主控制面板")

# 服务器控制区域
with st.container():
    st.subheader("🔧 服务器控制")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        host = st.text_input("主机地址", value="localhost", key="server_host")

    with col2:
        port = st.number_input("端口", min_value=1024, max_value=65535, value=8768, key="server_port")

    with col3:
        if st.button("🚀 启动服务器", disabled=st.session_state.server_running, use_container_width=True):
            if StartServerThread(host, port):
                st.success("服务器启动中...")
                time.sleep(1)
                st.rerun()

    with col4:
        if st.button("🛑 停止服务器", disabled=not st.session_state.server_running, use_container_width=True):
            StopServer()
            st.session_state.audio_playing = False
            st.success("服务器已停止")
            st.rerun()

st.divider()

# 音频播放控制区域
with st.container():
    st.subheader("🎵 音频播放控制")

    if st.session_state.server and st.session_state.server_running:
        server = st.session_state.server

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("▶️ 开始播放", disabled=server.audioStreaming, use_container_width=True):
                server.audioStreaming = True
                st.session_state.audio_playing = True
                # 为所有连接的客户端启动音频发送
                for clientWs in server.connectedClients.copy():
                    if not clientWs.closed:
                        asyncio.create_task(server.SendAudioStream(clientWs, clientWs.remote_address))
                st.success("音频播放已启动")
                st.rerun()

        with col2:
            if st.button("⏹️ 停止播放", disabled=not server.audioStreaming, use_container_width=True):
                server.audioStreaming = False
                st.session_state.audio_playing = False
                st.success("音频播放已停止")
                st.rerun()

        with col3:
            if st.button("🔄 重新加载音频", use_container_width=True):
                server.ReloadAudioFiles()
                st.success("音频文件已重新加载")
                st.rerun()

        # 显示当前播放状态
        if server.audioFilesData:
            current_file = server.audioFilesData[server.currentFileIndex]['file'] if server.audioFilesData else "无"
            st.info(f"📁 当前音频文件: {os.path.basename(current_file)}")
        else:
            st.warning("⚠️ 未找到音频文件，将使用模拟数据")
    else:
        st.warning("⚠️ 请先启动服务器")

st.divider()

# JSON消息发送区域
with st.container():
    st.subheader("📤 JSON消息发送")

    if st.session_state.server and st.session_state.server_running:
        server = st.session_state.server

        # 预设命令按钮
        st.write("**快速命令:**")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("📊 获取状态", use_container_width=True):
                SendJsonMessage(server, "command", {"cmd": "get_status"})
                st.rerun()

        with col2:
            if st.button("🎵 开始音频", use_container_width=True):
                SendJsonMessage(server, "command", {"cmd": "start_audio"})
                st.rerun()

        with col3:
            if st.button("⏹️ 停止音频", use_container_width=True):
                SendJsonMessage(server, "command", {"cmd": "stop_audio"})
                st.rerun()

        with col4:
            if st.button("🔄 重新加载", use_container_width=True):
                SendJsonMessage(server, "command", {"cmd": "reload_audio"})
                st.rerun()

        st.write("**自定义JSON消息:**")

        # 自定义JSON消息
        col1, col2 = st.columns([3, 1])

        with col1:
            custom_json = st.text_area(
                "JSON内容",
                value='{\n  "type": "command",\n  "data": {\n    "cmd": "get_status"\n  }\n}',
                height=120,
                help="输入要发送的JSON消息"
            )

        with col2:
            st.write("")  # 空行对齐
            st.write("")  # 空行对齐
            if st.button("📤 发送JSON", use_container_width=True):
                try:
                    json_data = json.loads(custom_json)
                    msg_type = json_data.get("type", "custom")
                    msg_data = json_data.get("data", {})
                    SendJsonMessage(server, msg_type, msg_data)
                    st.success("JSON消息已发送")
                    st.rerun()
                except json.JSONDecodeError as e:
                    st.error(f"JSON格式错误: {e}")
                except Exception as e:
                    st.error(f"发送失败: {e}")
    else:
        st.warning("⚠️ 请先启动服务器")

st.divider()

# 信息显示区域
col1, col2 = st.columns([3, 2])

with col1:
    st.header("📊 服务器状态监控")

    if st.session_state.server and st.session_state.server_running:
        server = st.session_state.server

        # 更新服务器状态
        server.UpdateServerStatus()
        status = server.serverStatus

        # 状态指标
        metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)

        with metric_col1:
            client_count = len([ws for ws in server.connectedClients if not ws.closed]) if server.connectedClients else 0
            st.metric("连接客户端", client_count)

        with metric_col2:
            st.metric("音频文件", status.get('audio_files', 0))

        with metric_col3:
            st.metric("音频包序号", status.get('seq_num', 0))

        with metric_col4:
            bytes_sent = f"{server.sendSuccBytes:,}" if hasattr(server, 'sendSuccBytes') else "0"
            st.metric("发送字节", bytes_sent)

        # 连接的客户端详情
        with st.expander("👥 连接的客户端详情", expanded=True):
            if server.connectedClients:
                active_clients = [ws for ws in server.connectedClients if not ws.closed]
                if active_clients:
                    for i, client in enumerate(active_clients, 1):
                        st.text(f"🔗 客户端 {i}: {client.remote_address[0]}:{client.remote_address[1]}")
                else:
                    st.info("所有客户端已断开连接")
            else:
                st.info("暂无客户端连接")

        # 服务器详细信息
        with st.expander("🔧 服务器详细信息"):
            info_data = {
                "服务器地址": f"ws://{host}:{port}",
                "服务器状态": status.get('server_status', 'unknown'),
                "启动时间": status.get('uptime', 'unknown'),
                "当前音频文件": os.path.basename(status.get('current_file', 'None')) if status.get('current_file') else 'None',
                "音频流状态": "运行中" if status.get('audio_streaming', False) else "已停止",
                "发送成功字节": f"{server.sendSuccBytes:,}" if hasattr(server, 'sendSuccBytes') else "0",
                "发送失败字节": f"{server.sendFailBytes:,}" if hasattr(server, 'sendFailBytes') else "0"
            }

            for key, value in info_data.items():
                st.text(f"**{key}**: {value}")

    else:
        st.warning("⚠️ 服务器未运行，无法显示状态信息")
        st.info("💡 请先启动服务器以查看详细状态")

with col2:
    st.header("📝 消息历史")

    # 消息历史显示
    with st.container():
        if st.session_state.message_history:
            # 显示最近的10条消息
            recent_messages = st.session_state.message_history[-10:]

            for msg in reversed(recent_messages):  # 最新的在上面
                with st.expander(f"[{msg['time']}] {msg['type']}", expanded=False):
                    st.code(msg['content'], language='json')
        else:
            st.info("暂无消息历史")

        # 清空消息历史按钮
        if st.button("🗑️ 清空消息历史", use_container_width=True):
            st.session_state.message_history = []
            st.rerun()

    st.divider()

    # 快速测试区域
    st.subheader("🧪 快速测试")

    if st.session_state.server and st.session_state.server_running:
        # 测试连接
        if st.button("🔗 测试连接", use_container_width=True):
            server = st.session_state.server
            client_count = len([ws for ws in server.connectedClients if not ws.closed]) if server.connectedClients else 0
            if client_count > 0:
                st.success(f"✅ 连接正常，{client_count} 个客户端在线")
            else:
                st.warning("⚠️ 暂无客户端连接")

        # 发送心跳
        if st.button("💓 发送心跳", use_container_width=True):
            SendJsonMessage(st.session_state.server, "ping", {})
            st.success("💓 心跳消息已发送")
            st.rerun()

        # 获取状态
        if st.button("📊 刷新状态", use_container_width=True):
            SendJsonMessage(st.session_state.server, "status", {})
            st.success("📊 状态请求已发送")
            st.rerun()
    else:
        st.warning("⚠️ 请先启动服务器")

st.divider()

# 侧边栏信息
with st.sidebar:
    st.header("📁 音频文件管理")

    # 获取音频文件列表
    audioFiles = GetAudioFiles()
    st.session_state.audio_files = audioFiles

    if audioFiles:
        st.success(f"✅ 找到 {len(audioFiles)} 个音频文件")

        for audioFile in audioFiles:
            with st.expander(f"🎵 {os.path.basename(audioFile)}", expanded=False):
                try:
                    fileSize = os.path.getsize(audioFile)
                    st.text(f"📏 大小: {FormatFileSize(fileSize)}")

                    # 如果是WAV文件，显示更多信息
                    if audioFile.lower().endswith('.wav'):
                        try:
                            import wave
                            with wave.open(audioFile, 'rb') as wav:
                                sampleRate = wav.getframerate()
                                channels = wav.getnchannels()
                                sampleWidth = wav.getsampwidth()
                                frames = wav.getnframes()
                                duration = frames / sampleRate

                                st.text(f"🔊 采样率: {sampleRate} Hz")
                                st.text(f"📻 声道: {channels}")
                                st.text(f"🎚️ 位深: {sampleWidth * 8} bit")
                                st.text(f"⏱️ 时长: {duration:.2f} 秒")
                        except Exception as e:
                            st.error(f"❌ 读取失败: {e}")

                except Exception as e:
                    st.error(f"❌ 获取信息失败: {e}")
    else:
        st.warning("⚠️ 未找到音频文件")
        st.info("💡 请在 'audio' 文件夹中放置 WAV 音频文件")

    st.divider()

    # 协议信息
    st.header("📋 协议信息")

    with st.expander("🔧 WebSocket音频包格式", expanded=False):
        st.code("""
struct AudioPacket {
    uint8_t version;     // 版本号 (1)
    uint8_t type;        // 类型 (1)
    uint16_t seqNum;     // 序列号
    uint16_t payloadLen; // 负载长度
    uint16_t reserved;   // 保留字段
    uint8_t data[];      // Opus音频数据
}
        """, language="c")

    with st.expander("📝 JSON命令格式", expanded=False):
        st.code("""
{
    "type": "command",
    "seq": 1001,
    "timestamp": 1234567890,
    "data": {
        "cmd": "start_audio"
    }
}
        """, language="json")

    with st.expander("📋 支持的命令", expanded=False):
        commands = [
            "start_audio - 开始音频流",
            "stop_audio - 停止音频流",
            "reload_audio - 重新加载音频文件",
            "get_status - 获取服务器状态"
        ]

        for cmd in commands:
            st.text(f"• {cmd}")

    st.divider()

    # 连接信息
    st.header("🔗 连接信息")

    if st.session_state.server_running:
        st.success(f"🌐 服务器地址:")
        st.code(f"ws://{host}:{port}")

        st.info("📱 测试客户端:")
        st.code("python3 sk_audio_test_client.py")
    else:
        st.warning("⚠️ 服务器未运行")

# 底部信息
st.divider()

# 使用说明
with st.expander("📖 使用说明", expanded=False):
    st.markdown("""
    ### 🚀 快速开始
    1. **启动服务器**: 点击"启动服务器"按钮
    2. **准备音频**: 将WAV文件放入'audio'文件夹
    3. **开始播放**: 点击"开始播放"按钮
    4. **发送消息**: 使用JSON消息发送功能
    5. **监控状态**: 查看实时服务器状态

    ### 🎵 音频要求
    - 格式: WAV文件
    - 推荐: 16kHz采样率，单声道
    - 位深: 16位PCM

    ### 📡 客户端连接
    - 地址: ws://localhost:8768
    - 协议: WebSocket
    - 支持: 二进制音频数据 + JSON命令
    """)

# 自动刷新（降低频率）
time.sleep(2)
st.rerun()
