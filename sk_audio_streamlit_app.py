#!/usr/bin/env python3
"""
SK音频WebSocket服务器 - Streamlit控制界面
基于websocket_audio_server.py的Web界面版本
"""

import streamlit as st
import asyncio
import threading
import time
import json
import os
import glob
from sk_audio_websocket_server import SkAudioWebSocketServer, GetServerInstance
import logging

# 配置页面
st.set_page_config(
    page_title="SK Audio WebSocket Server",
    page_icon="🎵",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化会话状态
if 'server' not in st.session_state:
    st.session_state.server = None
if 'server_thread' not in st.session_state:
    st.session_state.server_thread = None
if 'server_running' not in st.session_state:
    st.session_state.server_running = False
if 'server_logs' not in st.session_state:
    st.session_state.server_logs = []
if 'audio_files' not in st.session_state:
    st.session_state.audio_files = []

def StartServerThread(host: str, port: int):
    """在后台线程启动服务器"""
    def run_server():
        try:
            server = SkAudioWebSocketServer(host, port)
            st.session_state.server = server
            asyncio.run(server.Start())
        except Exception as e:
            logger.error(f"Server error: {e}")
            st.session_state.server_running = False
    
    if not st.session_state.server_running:
        st.session_state.server_running = True
        thread = threading.Thread(target=run_server, daemon=True)
        thread.start()
        st.session_state.server_thread = thread
        return True
    return False

def StopServer():
    """停止服务器"""
    if st.session_state.server:
        st.session_state.server.isPlaying = False
        st.session_state.server_running = False
        st.session_state.server = None

def GetAudioFiles():
    """获取音频文件列表"""
    audioDir = "audio"
    if not os.path.exists(audioDir):
        return []
    
    audioExtensions = ['*.wav', '*.WAV']
    audioFiles = []
    
    for extension in audioExtensions:
        pattern = os.path.join(audioDir, extension)
        audioFiles.extend(glob.glob(pattern))
    
    return audioFiles

def FormatFileSize(size: int) -> str:
    """格式化文件大小"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} TB"

# 主界面
st.title("🎵 SK Audio WebSocket Server")
st.markdown("基于websocket_audio_server.py的Streamlit控制界面")

# 侧边栏 - 服务器控制
with st.sidebar:
    st.header("🔧 服务器控制")
    
    # 服务器配置
    col1, col2 = st.columns(2)
    with col1:
        host = st.text_input("主机地址", value="localhost", key="server_host")
    with col2:
        port = st.number_input("端口", min_value=1024, max_value=65535, value=8768, key="server_port")
    
    # 服务器控制按钮
    col1, col2 = st.columns(2)
    with col1:
        if st.button("🚀 启动服务器", disabled=st.session_state.server_running):
            if StartServerThread(host, port):
                st.success("服务器启动中...")
                time.sleep(1)  # 等待服务器启动
                st.rerun()
    
    with col2:
        if st.button("🛑 停止服务器", disabled=not st.session_state.server_running):
            StopServer()
            st.success("服务器已停止")
            st.rerun()
    
    # 服务器状态
    if st.session_state.server_running:
        st.success(f"✅ 服务器运行中 - ws://{host}:{port}")
    else:
        st.error("❌ 服务器未运行")
    
    st.divider()
    
    # 音频控制
    st.header("🎵 音频控制")
    
    if st.session_state.server and st.session_state.server_running:
        server = st.session_state.server
        
        # 音频流控制
        col1, col2 = st.columns(2)
        with col1:
            if st.button("▶️ 开始音频流", disabled=server.audioStreaming):
                server.audioStreaming = True
                # 为所有连接的客户端启动音频发送
                for clientWs in server.connectedClients.copy():
                    if not clientWs.closed:
                        asyncio.create_task(server.SendAudioStream(clientWs, clientWs.remote_address))
                st.success("音频流已启动")
                st.rerun()
        
        with col2:
            if st.button("⏹️ 停止音频流", disabled=not server.audioStreaming):
                server.audioStreaming = False
                st.success("音频流已停止")
                st.rerun()
        
        # 重新加载音频文件
        if st.button("🔄 重新加载音频文件"):
            server.ReloadAudioFiles()
            st.success("音频文件已重新加载")
            st.rerun()
        
        # 音频流状态
        if server.audioStreaming:
            st.success("🎵 音频流：运行中")
        else:
            st.info("⏸️ 音频流：已停止")
    else:
        st.warning("请先启动服务器")

# 主内容区域
col1, col2 = st.columns([2, 1])

with col1:
    st.header("📊 服务器状态")
    
    if st.session_state.server and st.session_state.server_running:
        server = st.session_state.server
        
        # 更新服务器状态
        server.UpdateServerStatus()
        status = server.serverStatus
        
        # 状态指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("连接的客户端", status.get('connected_clients', 0))
        
        with col2:
            st.metric("音频文件数", status.get('audio_files', 0))
        
        with col3:
            st.metric("序列号", status.get('seq_num', 0))
        
        with col4:
            streaming_status = "运行中" if status.get('audio_streaming', False) else "停止"
            st.metric("音频流状态", streaming_status)
        
        # 详细状态信息
        st.subheader("详细状态")
        
        statusData = {
            "服务器状态": status.get('server_status', 'unknown'),
            "运行时间": status.get('uptime', 'unknown'),
            "当前音频文件": status.get('current_file', 'None'),
            "发送成功字节": f"{server.sendSuccBytes:,}",
            "发送失败字节": f"{server.sendFailBytes:,}"
        }
        
        for key, value in statusData.items():
            st.text(f"{key}: {value}")
        
        # 连接的客户端列表
        st.subheader("连接的客户端")
        if server.connectedClients:
            for i, client in enumerate(server.connectedClients, 1):
                if not client.closed:
                    st.text(f"👤 客户端 {i}: {client.remote_address}")
        else:
            st.info("暂无客户端连接")
        
    else:
        st.info("服务器未运行，无法显示状态信息")
    
    st.divider()
    
    # 测试命令
    st.header("🧪 测试命令")
    
    if st.session_state.server and st.session_state.server_running:
        server = st.session_state.server
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("📊 获取状态"):
                # 模拟发送状态命令
                testMsg = {
                    "type": "command",
                    "seq": int(time.time()),
                    "timestamp": int(time.time()),
                    "data": {"cmd": "get_status"}
                }
                st.json(testMsg)
        
        with col2:
            if st.button("🔄 重新加载"):
                # 模拟发送重新加载命令
                testMsg = {
                    "type": "command",
                    "seq": int(time.time()),
                    "timestamp": int(time.time()),
                    "data": {"cmd": "reload_audio"}
                }
                st.json(testMsg)
        
        with col3:
            if st.button("💓 心跳测试"):
                # 模拟发送心跳
                testMsg = {
                    "type": "ping",
                    "timestamp": int(time.time()),
                    "data": {}
                }
                st.json(testMsg)
    else:
        st.warning("请先启动服务器")

with col2:
    st.header("📁 音频文件管理")
    
    # 获取音频文件列表
    audioFiles = GetAudioFiles()
    st.session_state.audio_files = audioFiles
    
    if audioFiles:
        st.success(f"找到 {len(audioFiles)} 个音频文件")
        
        for audioFile in audioFiles:
            with st.expander(f"🎵 {os.path.basename(audioFile)}"):
                try:
                    fileSize = os.path.getsize(audioFile)
                    st.text(f"文件大小: {FormatFileSize(fileSize)}")
                    st.text(f"文件路径: {audioFile}")
                    
                    # 如果是WAV文件，显示更多信息
                    if audioFile.lower().endswith('.wav'):
                        try:
                            import wave
                            with wave.open(audioFile, 'rb') as wav:
                                sampleRate = wav.getframerate()
                                channels = wav.getnchannels()
                                sampleWidth = wav.getsampwidth()
                                frames = wav.getnframes()
                                duration = frames / sampleRate
                                
                                st.text(f"采样率: {sampleRate} Hz")
                                st.text(f"声道数: {channels}")
                                st.text(f"位深: {sampleWidth * 8} bit")
                                st.text(f"时长: {duration:.2f} 秒")
                        except Exception as e:
                            st.error(f"无法读取WAV文件信息: {e}")
                            
                except Exception as e:
                    st.error(f"无法获取文件信息: {e}")
    else:
        st.warning("未找到音频文件")
        st.info("请在 'audio' 文件夹中放置 WAV 音频文件")
    
    st.divider()
    
    # 协议信息
    st.header("📋 协议信息")
    
    st.subheader("WebSocket音频包格式")
    st.code("""
struct AudioPacket {
    uint8_t version;     // 版本号 (1)
    uint8_t type;        // 类型 (1)
    uint16_t seqNum;     // 序列号
    uint16_t payloadLen; // 负载长度
    uint16_t reserved;   // 保留字段
    uint8_t data[];      // Opus音频数据
}
    """, language="c")
    
    st.subheader("JSON命令格式")
    st.code("""
{
    "type": "command",
    "seq": 1001,
    "timestamp": 1234567890,
    "data": {
        "cmd": "start_audio"
    }
}
    """, language="json")
    
    st.subheader("支持的命令")
    commands = [
        "start_audio - 开始音频流",
        "stop_audio - 停止音频流", 
        "reload_audio - 重新加载音频文件",
        "get_status - 获取服务器状态"
    ]
    
    for cmd in commands:
        st.text(f"• {cmd}")

# 底部信息
st.divider()
st.markdown("### 📝 使用说明")
st.markdown("""
1. **启动服务器**: 点击"启动服务器"按钮启动WebSocket服务器
2. **音频文件**: 将WAV音频文件放置在'audio'文件夹中
3. **客户端连接**: 客户端可连接到 ws://localhost:8768
4. **音频流**: 点击"开始音频流"向连接的客户端发送音频数据
5. **监控**: 实时查看服务器状态和连接的客户端信息
""")

# 自动刷新
time.sleep(1)
st.rerun()
