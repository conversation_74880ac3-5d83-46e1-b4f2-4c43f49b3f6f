#!/usr/bin/env python3
"""
SK音频WebSocket测试客户端
用于测试SK音频WebSocket服务器
"""

import asyncio
import websockets
import json
import time
import sys
import threading
from queue import Queue
import struct
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SkAudioTestClient:
    def __init__(self, serverUrl: str = "ws://localhost:8768"):
        self.serverUrl = serverUrl
        self.websocket = None
        self.isConnected = False
        self.messageQueue = Queue()
        self.username = "SkTestClient"
        self.seqNum = 1
        
    async def Connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"Connecting to {self.serverUrl}...")
            self.websocket = await websockets.connect(self.serverUrl)
            self.isConnected = True
            print(f"✅ Connected to {self.serverUrl}")
            
            # 启动消息接收任务
            receiveTask = asyncio.create_task(self.ReceiveMessages())
            
            # 启动用户输入处理任务
            inputTask = asyncio.create_task(self.HandleUserInput())
            
            # 等待任一任务完成
            done, pending = await asyncio.wait([receiveTask, inputTask], return_when=asyncio.FIRST_COMPLETED)
            
            # 取消未完成的任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                    
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            self.isConnected = False
    
    async def ReceiveMessages(self):
        """接收消息循环"""
        try:
            async for message in self.websocket:
                if isinstance(message, str):
                    # JSON消息
                    await self.ProcessJsonMessage(message)
                elif isinstance(message, bytes):
                    # 二进制消息
                    await self.ProcessBinaryMessage(message)
        except websockets.exceptions.ConnectionClosed:
            print("🔌 Connection closed by server")
            self.isConnected = False
        except Exception as e:
            print(f"❌ Receive error: {e}")
            self.isConnected = False
    
    async def ProcessJsonMessage(self, message: str):
        """处理JSON消息"""
        try:
            data = json.loads(message)
            await self.DisplayJsonMessage(data)
        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse JSON: {e}")
    
    async def ProcessBinaryMessage(self, message: bytes):
        """处理二进制消息"""
        try:
            # 解析音频包头部
            if len(message) >= 8:
                header = struct.unpack('<BBHHH', message[:8])
                version = header[0]
                msgType = header[1]
                seqNum = header[2]
                payloadLen = header[3]
                reserved = header[4]
                payload = message[8:]
                
                print(f"\n[{time.strftime('%H:%M:%S')}] Received Audio Packet:")
                print(f"  📦 Version: {version}, Type: {msgType}")
                print(f"  🔢 SeqNum: {seqNum}, PayloadLen: {payloadLen}")
                print(f"  📊 Payload: {len(payload)} bytes")
                
                # 显示前几个字节的内容
                if len(payload) > 0:
                    preview = ' '.join([f'{b:02x}' for b in payload[:min(16, len(payload))]])
                    if len(payload) > 16:
                        preview += "..."
                    print(f"  🔍 Data: {preview}")
            else:
                print(f"\n[{time.strftime('%H:%M:%S')}] Received Binary: {len(message)} bytes")
                
        except Exception as e:
            print(f"❌ Failed to parse binary message: {e}")
        
        print(">>> ", end="", flush=True)
    
    async def DisplayJsonMessage(self, data: dict):
        """显示JSON消息"""
        msgType = data.get('type', '')
        timestamp = data.get('timestamp', 0)
        timeStr = time.strftime('%H:%M:%S', time.localtime(timestamp))
        
        print(f"\n[{timeStr}] Received {msgType}:")
        
        if msgType == 'notification':
            msgData = data.get('data', {})
            event = msgData.get('event', 'notification')
            message = msgData.get('message', '')
            print(f"  🎉 {event}: {message}")
            
            if 'client_id' in msgData:
                print(f"  👤 Client ID: {msgData['client_id']}")
            if 'connected_clients' in msgData:
                print(f"  👥 Connected clients: {msgData['connected_clients']}")
            
        elif msgType == 'response':
            result = data.get('result', 'unknown')
            message = data.get('message', '')
            responseData = data.get('data', {})
            
            if result == 'success':
                print(f"  ✅ {message}")
            else:
                print(f"  ❌ {message}")
                
            if responseData:
                print(f"  📊 Data:")
                for key, value in responseData.items():
                    print(f"    {key}: {value}")
                
        elif msgType == 'pong':
            print(f"  💓 Pong received")
            
        else:
            print(f"  ❓ Unknown message type: {msgType}")
            print(f"  📄 Data: {json.dumps(data, indent=2)}")
        
        print(">>> ", end="", flush=True)
    
    async def SendMessage(self, message: dict):
        """发送JSON消息"""
        if self.websocket and self.isConnected:
            try:
                await self.websocket.send(json.dumps(message))
            except Exception as e:
                print(f"❌ Send error: {e}")
                self.isConnected = False
    
    async def SendCommand(self, cmd: str):
        """发送命令"""
        cmdMsg = {
            "type": "command",
            "seq": self.seqNum,
            "timestamp": int(time.time()),
            "data": {
                "cmd": cmd
            }
        }
        self.seqNum += 1
        await self.SendMessage(cmdMsg)
    
    async def SendPing(self):
        """发送ping"""
        pingMsg = {
            "type": "ping",
            "timestamp": int(time.time()),
            "data": {}
        }
        await self.SendMessage(pingMsg)
    
    async def SendStatusRequest(self):
        """发送状态请求"""
        statusMsg = {
            "type": "status",
            "seq": self.seqNum,
            "timestamp": int(time.time()),
            "data": {}
        }
        self.seqNum += 1
        await self.SendMessage(statusMsg)
    
    async def HandleUserInput(self):
        """处理用户输入"""
        inputQueue = Queue()
        
        def inputThread():
            """输入线程"""
            while self.isConnected:
                try:
                    userInput = input()
                    inputQueue.put(userInput)
                except EOFError:
                    inputQueue.put(None)
                    break
                except Exception as e:
                    print(f"Input error: {e}")
                    break
        
        # 启动输入线程
        thread = threading.Thread(target=inputThread, daemon=True)
        thread.start()
        
        print("\n" + "="*60)
        print("SK Audio WebSocket Test Client Commands:")
        print("  start           - Send start_audio command")
        print("  stop            - Send stop_audio command")
        print("  reload          - Send reload_audio command")
        print("  status          - Get server status")
        print("  ping            - Send ping")
        print("  help            - Show this help")
        print("  quit            - Disconnect and exit")
        print("="*60)
        print(">>> ", end="", flush=True)
        
        while self.isConnected:
            try:
                # 非阻塞检查用户输入
                try:
                    userInput = inputQueue.get_nowait()
                except:
                    await asyncio.sleep(0.1)
                    continue
                
                if userInput is None:
                    break
                
                userInput = userInput.strip().lower()
                if not userInput:
                    print(">>> ", end="", flush=True)
                    continue
                
                # 解析命令
                if userInput == 'start':
                    await self.SendCommand('start_audio')
                    
                elif userInput == 'stop':
                    await self.SendCommand('stop_audio')
                    
                elif userInput == 'reload':
                    await self.SendCommand('reload_audio')
                    
                elif userInput == 'status':
                    await self.SendStatusRequest()
                    
                elif userInput == 'ping':
                    await self.SendPing()
                    
                elif userInput == 'help':
                    print("\nCommands:")
                    print("  start           - Send start_audio command")
                    print("  stop            - Send stop_audio command")
                    print("  reload          - Send reload_audio command")
                    print("  status          - Get server status")
                    print("  ping            - Send ping")
                    print("  help            - Show this help")
                    print("  quit            - Disconnect and exit")
                    
                elif userInput == 'quit':
                    print("Disconnecting...")
                    break
                    
                else:
                    print(f"Unknown command: {userInput}. Type 'help' for available commands.")
                
                if self.isConnected:
                    print(">>> ", end="", flush=True)
                    
            except Exception as e:
                print(f"Input handling error: {e}")
                await asyncio.sleep(0.1)
    
    async def Disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
        self.isConnected = False

async def main():
    """主函数"""
    serverUrl = "ws://localhost:8768"
    
    if len(sys.argv) > 1:
        serverUrl = sys.argv[1]
    
    print("SK Audio WebSocket Test Client")
    print(f"Server: {serverUrl}")
    print("-" * 40)
    
    client = SkAudioTestClient(serverUrl)
    
    try:
        await client.Connect()
    except KeyboardInterrupt:
        print("\nClient stopped by user")
    except Exception as e:
        print(f"Client error: {e}")
    finally:
        await client.Disconnect()
        print("Client disconnected")

if __name__ == "__main__":
    asyncio.run(main())
