#!/usr/bin/env python3
"""
SK音频WebSocket服务器 - 独立Streamlit应用
完全内置WebSocket服务器，无需外部依赖
"""

import streamlit as st
import asyncio
import websockets
import threading
import time
import json
import os
import glob
import struct
import numpy as np
import logging
from queue import Queue
from typing import Set, Dict, Any

# 配置页面
st.set_page_config(
    page_title="SK Audio WebSocket Server",
    page_icon="🎵",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StandaloneAudioServer:
    """独立的音频WebSocket服务器"""
    
    def __init__(self, host: str = "localhost", port: int = 8768):
        self.host = host
        self.port = port
        self.connectedClients: Set[websockets.WebSocketServerProtocol] = set()
        self.clientCounter = 0
        self.maxClients = 5
        
        # 音频相关
        self.audioFilesData = []
        self.currentFileIndex = 0
        self.audioQueue = Queue()
        self.audioThread = None
        self.isPlaying = False
        self.audioStreaming = False
        
        # 统计信息
        self.seqNum = 0
        self.sendSuccBytes = 0
        self.sendFailBytes = 0
        
        # 服务器状态
        self.isRunning = False
        self.server = None
        
        # 自动加载音频文件
        self.LoadAudioFiles()
    
    def LoadAudioFiles(self):
        """加载音频文件"""
        audioDir = "audio"
        if not os.path.exists(audioDir):
            logger.info(f"音频文件夹 '{audioDir}' 不存在")
            return
        
        audioFiles = glob.glob(os.path.join(audioDir, "*.wav"))
        self.audioFilesData = []
        
        for audioFile in audioFiles:
            try:
                import wave
                with wave.open(audioFile, 'rb') as wav:
                    sampleRate = wav.getframerate()
                    channels = wav.getnchannels()
                    frames = wav.readframes(wav.getnframes())
                    
                    # 转换为numpy数组
                    audioData = np.frombuffer(frames, dtype=np.int16)
                    
                    # 如果是立体声，转换为单声道
                    if channels == 2:
                        audioData = audioData.reshape(-1, 2)
                        audioData = np.mean(audioData, axis=1).astype(np.int16)
                    
                    # 重采样到16kHz（简化处理）
                    if sampleRate != 16000:
                        ratio = 16000 / sampleRate
                        newLength = int(len(audioData) * ratio)
                        oldIndices = np.linspace(0, len(audioData) - 1, newLength)
                        audioData = np.interp(oldIndices, np.arange(len(audioData)), audioData).astype(np.int16)
                    
                    self.audioFilesData.append({
                        'file': audioFile,
                        'data': audioData,
                        'pos': 0,
                        'duration': len(audioData) / 16000
                    })
                    
                    logger.info(f"加载音频文件: {audioFile}")
                    
            except Exception as e:
                logger.error(f"加载音频文件失败: {audioFile} - {e}")
        
        if self.audioFilesData:
            logger.info(f"已加载 {len(self.audioFilesData)} 个音频文件")
            self.StartAudioThread()
    
    def StartAudioThread(self):
        """启动音频处理线程"""
        if self.audioThread and self.audioThread.is_alive():
            return
            
        self.isPlaying = True
        self.audioThread = threading.Thread(target=self.AudioWorker, daemon=True)
        self.audioThread.start()
    
    def AudioWorker(self):
        """音频处理工作线程"""
        frameSize = 320  # 16kHz * 0.02s = 320 samples per 20ms frame
        
        while self.isPlaying:
            if not self.audioFilesData:
                # 生成模拟数据
                opusData = self.GenerateSimulatedOpusData()
                self.audioQueue.put(opusData)
            else:
                # 处理真实音频文件
                currentFileData = self.audioFilesData[self.currentFileIndex]
                audioData = currentFileData['data']
                audioPos = currentFileData['pos']
                
                if audioPos + frameSize >= len(audioData):
                    # 切换到下一个文件
                    self.currentFileIndex = (self.currentFileIndex + 1) % len(self.audioFilesData)
                    self.audioFilesData[self.currentFileIndex]['pos'] = 0
                    continue
                
                # 获取一帧音频数据
                frame = audioData[audioPos:audioPos + frameSize]
                self.audioFilesData[self.currentFileIndex]['pos'] = audioPos + frameSize
                
                # 简单的伪Opus编码
                opusData = self.EncodeToOpus(frame)
                self.audioQueue.put(opusData)
            
            time.sleep(0.02)  # 20ms间隔
    
    def EncodeToOpus(self, pcmFrame: np.ndarray) -> bytes:
        """简单的伪Opus编码"""
        opusHeader = b'\xFC\x00'  # 伪Opus标识
        pcmBytes = pcmFrame.astype(np.int16).tobytes()
        return opusHeader + pcmBytes[:640]  # 最大640字节
    
    def GenerateSimulatedOpusData(self) -> bytes:
        """生成模拟的Opus数据"""
        frameSize = 60
        opusData = bytearray(frameSize)
        opusData[0] = 0xFC
        opusData[1] = frameSize & 0xFF
        for i in range(2, frameSize):
            opusData[i] = (self.seqNum + i) & 0xFF
        return bytes(opusData)
    
    def CreateAudioPacket(self, opusData: bytes) -> bytes:
        """创建WebSocket音频包"""
        version = 0x01
        audioType = 0x01
        payloadLen = len(opusData)
        reserved = 0x00
        
        header = struct.pack('<BBHHH', 
                           version, audioType, self.seqNum & 0xFFFF, 
                           payloadLen, reserved)
        
        self.seqNum += 1
        return header + opusData
    
    async def HandleClient(self, websocket: websockets.WebSocketServerProtocol, path: str):
        """处理客户端连接"""
        clientAddr = websocket.remote_address
        self.clientCounter += 1
        
        logger.info(f"客户端 {self.clientCounter} 连接: {clientAddr}")
        self.connectedClients.add(websocket)
        
        try:
            # 发送欢迎消息
            welcomeMsg = {
                "type": "notification",
                "timestamp": int(time.time()),
                "data": {
                    "event": "connected",
                    "message": "Welcome to SK Audio WebSocket Server",
                    "client_id": self.clientCounter,
                    "connected_clients": len(self.connectedClients)
                }
            }
            await websocket.send(json.dumps(welcomeMsg))
            
            # 处理消息
            async for message in websocket:
                if isinstance(message, str):
                    await self.HandleTextMessage(websocket, message, clientAddr)
                elif isinstance(message, bytes):
                    logger.info(f"收到二进制消息: {len(message)} 字节")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"客户端断开连接: {clientAddr}")
        except Exception as e:
            logger.error(f"客户端错误 {clientAddr}: {e}")
        finally:
            self.connectedClients.discard(websocket)
            logger.info(f"客户端 {clientAddr} 清理完成")
    
    async def HandleTextMessage(self, websocket, message: str, clientAddr):
        """处理文本消息"""
        try:
            data = json.loads(message)
            msgType = data.get('type', '')
            
            if msgType == 'command':
                await self.HandleCommand(websocket, data, clientAddr)
            elif msgType == 'status':
                await self.HandleStatusRequest(websocket, data, clientAddr)
            else:
                logger.warning(f"未知消息类型: {msgType}")
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
        except Exception as e:
            logger.error(f"处理消息错误: {e}")
    
    async def HandleCommand(self, websocket, data: dict, clientAddr):
        """处理命令消息"""
        cmdData = data.get('data', {})
        cmd = cmdData.get('cmd', '')
        seq = data.get('seq', 0)
        
        logger.info(f"处理命令 '{cmd}' 来自 {clientAddr}")
        
        result = "success"
        message = f"命令 '{cmd}' 处理成功"
        
        if cmd == "start_audio":
            self.audioStreaming = True
            message = "音频流已启动"
        elif cmd == "stop_audio":
            self.audioStreaming = False
            message = "音频流已停止"
        elif cmd == "reload_audio":
            self.LoadAudioFiles()
            message = f"音频文件已重新加载，找到 {len(self.audioFilesData)} 个文件"
        elif cmd == "get_status":
            message = "状态获取成功"
        else:
            result = "error"
            message = f"未知命令: {cmd}"
        
        response = {
            "type": "response",
            "seq": seq,
            "timestamp": int(time.time()),
            "result": result,
            "message": message,
            "data": {
                "command": cmd,
                "audio_streaming": self.audioStreaming,
                "processed_at": time.strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        await websocket.send(json.dumps(response))
    
    async def HandleStatusRequest(self, websocket, data: dict, clientAddr):
        """处理状态请求"""
        seq = data.get('seq', 0)
        
        statusData = {
            "server_status": "running",
            "connected_clients": len(self.connectedClients),
            "audio_files": len(self.audioFilesData),
            "current_file": self.audioFilesData[self.currentFileIndex]['file'] if self.audioFilesData else None,
            "seq_num": self.seqNum,
            "audio_streaming": self.audioStreaming,
            "uptime": time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        response = {
            "type": "response",
            "seq": seq,
            "timestamp": int(time.time()),
            "result": "success",
            "message": "状态获取成功",
            "data": statusData
        }
        
        await websocket.send(json.dumps(response))
    
    async def SendAudioToClients(self):
        """向客户端发送音频数据"""
        while self.isRunning:
            if self.audioStreaming and self.connectedClients:
                if not self.audioQueue.empty():
                    opusData = self.audioQueue.get()
                    packet = self.CreateAudioPacket(opusData)
                    
                    # 向所有连接的客户端发送
                    disconnected = set()
                    for websocket in self.connectedClients:
                        try:
                            await websocket.send(packet)
                        except websockets.exceptions.ConnectionClosed:
                            disconnected.add(websocket)
                        except Exception as e:
                            logger.error(f"发送音频包错误: {e}")
                            disconnected.add(websocket)
                    
                    # 移除断开连接的客户端
                    self.connectedClients -= disconnected
            
            await asyncio.sleep(0.02)  # 20ms间隔
    
    async def Start(self):
        """启动服务器"""
        try:
            self.isRunning = True
            logger.info(f"启动SK音频WebSocket服务器: {self.host}:{self.port}")
            
            # 启动WebSocket服务器
            self.server = await websockets.serve(self.HandleClient, self.host, self.port)
            
            # 启动音频发送任务
            audioTask = asyncio.create_task(self.SendAudioToClients())
            
            logger.info(f"服务器已启动: ws://{self.host}:{self.port}")
            
            # 等待服务器运行
            await asyncio.Future()  # 永远等待
            
        except Exception as e:
            logger.error(f"服务器启动失败: {e}")
            raise e
        finally:
            self.isRunning = False
            if hasattr(self, 'audioTask'):
                audioTask.cancel()
    
    def Stop(self):
        """停止服务器"""
        self.isRunning = False
        self.audioStreaming = False
        self.isPlaying = False
        if self.server:
            self.server.close()

# 全局服务器实例
if 'audio_server' not in st.session_state:
    st.session_state.audio_server = None
if 'server_thread' not in st.session_state:
    st.session_state.server_thread = None
if 'server_running' not in st.session_state:
    st.session_state.server_running = False
if 'server_error' not in st.session_state:
    st.session_state.server_error = None
if 'message_history' not in st.session_state:
    st.session_state.message_history = []

def StartServer(host: str, port: int):
    """启动服务器"""
    def run_server():
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            server = StandaloneAudioServer(host, port)
            st.session_state.audio_server = server

            loop.run_until_complete(server.Start())
        except OSError as e:
            if "10048" in str(e) or "Address already in use" in str(e):
                st.session_state.server_error = f"端口 {port} 已被占用"
            else:
                st.session_state.server_error = f"服务器启动失败: {e}"
            st.session_state.server_running = False
        except Exception as e:
            st.session_state.server_error = f"服务器错误: {e}"
            st.session_state.server_running = False
        finally:
            try:
                loop.close()
            except:
                pass

    if not st.session_state.server_running:
        st.session_state.server_running = True
        st.session_state.server_error = None
        thread = threading.Thread(target=run_server, daemon=True)
        thread.start()
        st.session_state.server_thread = thread
        return True
    return False

def StopServer():
    """停止服务器"""
    if st.session_state.audio_server:
        st.session_state.audio_server.Stop()
        st.session_state.audio_server = None
    st.session_state.server_running = False
    st.session_state.server_error = None

def GetAudioFiles():
    """获取音频文件列表"""
    audioDir = "audio"
    if not os.path.exists(audioDir):
        return []
    return glob.glob(os.path.join(audioDir, "*.wav"))

def FormatFileSize(size: int) -> str:
    """格式化文件大小"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} TB"

def SendJsonMessage(messageType: str, data: dict = None):
    """记录JSON消息"""
    message = {
        "type": messageType,
        "timestamp": int(time.time()),
        "data": data or {}
    }

    st.session_state.message_history.append({
        "time": time.strftime('%H:%M:%S'),
        "type": "发送",
        "content": json.dumps(message, ensure_ascii=False, indent=2)
    })

    return message

# 主界面
st.title("🎵 SK Audio WebSocket Server")
st.markdown("**独立Streamlit应用 - 内置WebSocket服务器**")

# 状态指示器
col1, col2, col3 = st.columns(3)
with col1:
    if st.session_state.server_running:
        if st.session_state.server_error:
            st.error("🔴 服务器启动失败")
        else:
            st.success("🟢 服务器运行中")
    else:
        st.error("🔴 服务器未运行")

with col2:
    if st.session_state.audio_server and st.session_state.audio_server.audioStreaming:
        st.success("🎵 音频播放中")
    else:
        st.info("⏸️ 音频已停止")

with col3:
    if st.session_state.audio_server and st.session_state.audio_server.connectedClients:
        client_count = len([ws for ws in st.session_state.audio_server.connectedClients if not ws.closed])
        st.info(f"👥 {client_count} 个客户端连接")
    else:
        st.info("👥 无客户端连接")

# 显示服务器错误信息
if st.session_state.server_error:
    st.error(f"❌ {st.session_state.server_error}")
    if "端口" in st.session_state.server_error and "占用" in st.session_state.server_error:
        st.info("💡 建议：请尝试使用其他端口号，或者关闭占用该端口的程序")

st.divider()

# 主控制面板
st.header("🎛️ 主控制面板")

# 服务器控制区域
with st.container():
    st.subheader("🔧 服务器控制")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        host = st.text_input("主机地址", value="localhost", key="server_host")

    with col2:
        port = st.number_input("端口", min_value=1024, max_value=65535, value=8768, key="server_port")

    with col3:
        if st.button("🚀 启动服务器", disabled=st.session_state.server_running, use_container_width=True):
            if StartServer(host, port):
                st.info("服务器启动中，请稍候...")
                time.sleep(2)
                st.rerun()

    with col4:
        if st.button("🛑 停止服务器", disabled=not st.session_state.server_running, use_container_width=True):
            StopServer()
            st.success("服务器已停止")
            st.rerun()

st.divider()

# 音频播放控制区域
with st.container():
    st.subheader("🎵 音频播放控制")

    if st.session_state.audio_server and st.session_state.server_running and not st.session_state.server_error:
        server = st.session_state.audio_server

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("▶️ 开始播放", disabled=server.audioStreaming, use_container_width=True):
                server.audioStreaming = True
                active_clients = [ws for ws in server.connectedClients if not ws.closed]
                if active_clients:
                    st.success(f"音频播放已启动，向 {len(active_clients)} 个客户端发送")
                else:
                    st.warning("音频播放已启动，但暂无客户端连接")
                st.rerun()

        with col2:
            if st.button("⏹️ 停止播放", disabled=not server.audioStreaming, use_container_width=True):
                server.audioStreaming = False
                st.success("音频播放已停止")
                st.rerun()

        with col3:
            if st.button("🔄 重新加载音频", use_container_width=True):
                server.LoadAudioFiles()
                st.success("音频文件已重新加载")
                st.rerun()

        # 显示当前播放状态
        if server.audioFilesData:
            current_file = server.audioFilesData[server.currentFileIndex]['file']
            st.info(f"📁 当前音频文件: {os.path.basename(current_file)}")
        else:
            st.warning("⚠️ 未找到音频文件，将使用模拟数据")
    else:
        st.warning("⚠️ 请先启动服务器")

st.divider()

# JSON消息发送区域
with st.container():
    st.subheader("📤 JSON消息发送")

    if st.session_state.audio_server and st.session_state.server_running and not st.session_state.server_error:
        # 预设命令按钮
        st.write("**快速命令:**")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("📊 获取状态", use_container_width=True):
                SendJsonMessage("command", {"cmd": "get_status"})
                st.success("状态请求已记录")
                st.rerun()

        with col2:
            if st.button("🎵 开始音频", use_container_width=True):
                SendJsonMessage("command", {"cmd": "start_audio"})
                st.success("开始音频命令已记录")
                st.rerun()

        with col3:
            if st.button("⏹️ 停止音频", use_container_width=True):
                SendJsonMessage("command", {"cmd": "stop_audio"})
                st.success("停止音频命令已记录")
                st.rerun()

        with col4:
            if st.button("🔄 重新加载", use_container_width=True):
                SendJsonMessage("command", {"cmd": "reload_audio"})
                st.success("重新加载命令已记录")
                st.rerun()

        st.write("**自定义JSON消息:**")

        # 自定义JSON消息
        col1, col2 = st.columns([3, 1])

        with col1:
            custom_json = st.text_area(
                "JSON内容",
                value='{\n  "type": "command",\n  "data": {\n    "cmd": "get_status"\n  }\n}',
                height=120,
                help="输入要发送的JSON消息"
            )

        with col2:
            st.write("")
            st.write("")
            if st.button("📤 发送JSON", use_container_width=True):
                try:
                    json_data = json.loads(custom_json)
                    msg_type = json_data.get("type", "custom")
                    msg_data = json_data.get("data", {})
                    SendJsonMessage(msg_type, msg_data)
                    st.success("JSON消息已记录")
                    st.rerun()
                except json.JSONDecodeError as e:
                    st.error(f"JSON格式错误: {e}")
    else:
        st.warning("⚠️ 请先启动服务器")

# 自动刷新
time.sleep(2)
st.rerun()
