# Streamlit WebSocket服务器使用说明

## 项目概述

这是一个基于Streamlit和WebSocket的实时通信系统，包含以下组件：

1. **WebSocket服务器** (`streamlit_websocket_server.py`) - 处理实时消息传输
2. **Streamlit前端** (`streamlit_app.py`) - 提供用户界面
3. **测试客户端** (`websocket_test_client.py`) - 用于测试和调试

## 功能特性

### WebSocket服务器功能
- ✅ 实时消息传输
- ✅ 多客户端连接管理
- ✅ 消息历史记录
- ✅ 命令处理系统
- ✅ 连接保护机制（防止频繁重连）
- ✅ 心跳检测
- ✅ 广播消息

### Streamlit前端功能
- ✅ 实时聊天界面
- ✅ 服务器状态监控
- ✅ 用户管理
- ✅ 消息统计
- ✅ 命令控制面板

## 安装和启动

### 1. 安装依赖
```bash
pip install -r requirements_streamlit.txt
```

### 2. 快速启动（推荐）
```bash
./start_streamlit_server.sh
```

### 3. 手动启动

#### 启动WebSocket服务器
```bash
python3 streamlit_websocket_server.py [host] [port]
# 默认: localhost 8765
```

#### 启动Streamlit应用
```bash
streamlit run streamlit_app.py
```

## 使用方法

### 1. 连接到服务器
1. 打开Streamlit应用（通常在 http://localhost:8501）
2. 在侧边栏输入服务器URL（默认：ws://localhost:8765）
3. 输入用户名
4. 点击"连接"按钮

### 2. 发送消息
- 在聊天输入框中输入消息
- 按Enter或点击"发送"按钮

### 3. 服务器命令
在侧边栏可以执行以下命令：
- **获取状态** - 查看服务器运行状态
- **获取用户列表** - 查看在线用户
- **清空历史** - 清除消息历史
- **发送心跳** - 测试连接

## 测试客户端使用

### 启动测试客户端
```bash
python3 websocket_test_client.py [server_url]
# 默认: ws://localhost:8765
```

### 测试客户端命令
- `chat <message>` - 发送聊天消息
- `status` - 获取服务器状态
- `clients` - 获取客户端列表
- `clear` - 清空消息历史
- `ping` - 发送心跳
- `username <name>` - 更改用户名
- `help` - 显示帮助
- `quit` - 退出客户端

## 消息协议

### 消息类型

#### 1. 聊天消息
```json
{
    "type": "chat",
    "timestamp": 1234567890,
    "data": {
        "username": "用户名",
        "message": "消息内容"
    }
}
```

#### 2. 命令消息
```json
{
    "type": "command",
    "seq": 1001,
    "timestamp": 1234567890,
    "data": {
        "cmd": "get_status"
    }
}
```

#### 3. 响应消息
```json
{
    "type": "response",
    "seq": 1001,
    "timestamp": 1234567890,
    "result": "success",
    "message": "操作成功",
    "data": {
        "server_status": "running",
        "connected_clients": 3
    }
}
```

#### 4. 通知消息
```json
{
    "type": "notification",
    "timestamp": 1234567890,
    "data": {
        "event": "user_joined",
        "message": "用户加入聊天",
        "connected_clients": 3
    }
}
```

## 配置选项

### 服务器配置
- **host**: 服务器监听地址（默认：localhost）
- **port**: 服务器端口（默认：8765）
- **maxClients**: 最大客户端连接数（默认：10）
- **maxHistorySize**: 消息历史最大数量（默认：100）

### Streamlit配置
- **server.port**: Streamlit端口（默认：8501）
- **server.address**: Streamlit地址（默认：localhost）

## 故障排除

### 1. 连接失败
- 检查WebSocket服务器是否启动
- 确认服务器地址和端口正确
- 检查防火墙设置

### 2. 消息不显示
- 刷新Streamlit页面
- 检查浏览器控制台错误
- 重新连接服务器

### 3. 服务器无响应
- 检查服务器日志
- 重启WebSocket服务器
- 检查系统资源使用情况

## 扩展功能

### 1. 添加新的消息类型
在`streamlit_websocket_server.py`的`HandleMessage`方法中添加新的消息类型处理。

### 2. 自定义命令
在`HandleCommand`方法中添加新的命令处理逻辑。

### 3. 数据持久化
可以添加数据库支持来持久化消息历史和用户信息。

### 4. 文件传输
可以扩展协议支持文件上传和下载功能。

## 安全注意事项

1. **生产环境部署**：
   - 使用HTTPS/WSS协议
   - 添加身份验证
   - 限制连接速率

2. **输入验证**：
   - 验证消息长度
   - 过滤恶意内容
   - 防止XSS攻击

3. **资源限制**：
   - 限制连接数
   - 限制消息频率
   - 监控内存使用

## 技术架构

```
┌─────────────────┐    WebSocket    ┌──────────────────┐
│  Streamlit App  │ ←──────────────→ │  WebSocket Server │
│  (Frontend)     │                 │   (Backend)      │
└─────────────────┘                 └──────────────────┘
        ↑                                    ↑
        │                                    │
   HTTP/Browser                         TCP/WebSocket
        │                                    │
        ↓                                    ↓
┌─────────────────┐                 ┌──────────────────┐
│     用户        │                 │   测试客户端      │
└─────────────────┘                 └──────────────────┘
```

## 版本历史

- **v1.0.0** - 初始版本，基本聊天功能
- 支持实时消息传输
- 支持多客户端连接
- 支持基本命令系统

## 联系和支持

如有问题或建议，请查看项目文档或联系开发团队。
