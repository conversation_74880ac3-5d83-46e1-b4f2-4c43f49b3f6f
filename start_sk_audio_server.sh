#!/bin/bash
# SK音频WebSocket服务器启动脚本

echo "========================================"
echo "SK音频WebSocket服务器启动脚本"
echo "========================================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查依赖包
echo "📦 检查依赖包..."
pip3 install -r requirements_streamlit.txt

# 检查audio文件夹
if [ ! -d "audio" ]; then
    echo "📁 创建audio文件夹..."
    mkdir audio
    echo "ℹ️  请将WAV音频文件放置在audio文件夹中"
fi

# 启动WebSocket服务器（后台运行）
echo "🚀 启动SK音频WebSocket服务器..."
python3 sk_audio_websocket_server.py localhost 8768 &
SERVER_PID=$!

# 等待服务器启动
sleep 3

# 检查服务器是否启动成功
if ps -p $SERVER_PID > /dev/null; then
    echo "✅ SK音频WebSocket服务器已启动 (PID: $SERVER_PID)"
else
    echo "❌ SK音频WebSocket服务器启动失败"
    exit 1
fi

# 启动Streamlit应用
echo "🌐 启动Streamlit控制界面..."
streamlit run sk_audio_streamlit_app.py --server.port 8501 --server.address localhost

# 清理：当Streamlit应用退出时，停止WebSocket服务器
echo "🛑 停止SK音频WebSocket服务器..."
kill $SERVER_PID 2>/dev/null

echo "✅ 服务器已停止"
