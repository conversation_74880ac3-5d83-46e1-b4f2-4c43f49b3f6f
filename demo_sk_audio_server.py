#!/usr/bin/env python3
"""
SK音频WebSocket服务器演示脚本
展示如何使用SK音频WebSocket服务器的各种功能
"""

import asyncio
import time
import json
import os
import sys
from sk_audio_websocket_server import SkAudioWebSocketServer
from sk_audio_test_client import SkAudioTestClient

async def DemoServer():
    """演示服务器功能"""
    print("=" * 60)
    print("SK音频WebSocket服务器演示")
    print("=" * 60)
    
    # 创建服务器实例
    server = SkAudioWebSocketServer("localhost", 8768)
    
    print("🚀 启动服务器...")
    
    # 在后台启动服务器
    serverTask = asyncio.create_task(server.Start())
    
    # 等待服务器启动
    await asyncio.sleep(2)
    
    print("✅ 服务器已启动")
    print(f"📡 监听地址: ws://localhost:8768")
    print(f"🎵 音频文件数: {len(server.audioFilesData)}")
    
    return server, serverTask

async def DemoClient():
    """演示客户端功能"""
    print("\n🔗 启动测试客户端...")
    
    client = SkAudioTestClient("ws://localhost:8768")
    
    # 连接到服务器
    try:
        await client.websocket.connect("ws://localhost:8768") if hasattr(client, 'websocket') else None
        client.websocket = await asyncio.wait_for(
            client.Connect(), timeout=5.0
        )
        print("✅ 客户端连接成功")
    except Exception as e:
        print(f"❌ 客户端连接失败: {e}")
        return None
    
    return client

async def DemoCommands(client):
    """演示命令功能"""
    if not client:
        return
        
    print("\n📋 演示命令功能...")
    
    commands = [
        ("get_status", "获取服务器状态"),
        ("start_audio", "开始音频流"),
        ("stop_audio", "停止音频流"),
        ("reload_audio", "重新加载音频文件")
    ]
    
    for cmd, desc in commands:
        print(f"\n🔧 执行命令: {cmd} ({desc})")
        
        # 发送命令
        await client.SendCommand(cmd)
        
        # 等待响应
        await asyncio.sleep(1)

async def DemoAudioStream(server):
    """演示音频流功能"""
    print("\n🎵 演示音频流功能...")
    
    if not server.audioFilesData:
        print("⚠️  没有音频文件，使用模拟数据")
    
    # 启动音频流
    print("▶️  启动音频流...")
    server.audioStreaming = True
    
    # 模拟发送几个音频包
    for i in range(5):
        opusData = server.GetAudioData()
        if opusData:
            packet = server.messageHandler.protocol.CreateAudioPacket(opusData)
            print(f"📦 创建音频包 #{i+1}: {len(packet)} 字节")
        else:
            # 生成模拟数据
            opusData = server.audioProcessor.GenerateSimulatedOpusData(i)
            packet = server.messageHandler.protocol.CreateAudioPacket(opusData)
            print(f"📦 创建模拟音频包 #{i+1}: {len(packet)} 字节")
        
        await asyncio.sleep(0.5)
    
    # 停止音频流
    print("⏹️  停止音频流...")
    server.audioStreaming = False

def CreateDemoAudioFiles():
    """创建演示音频文件"""
    audioDir = "audio"
    if not os.path.exists(audioDir):
        os.makedirs(audioDir)
        print(f"📁 创建音频文件夹: {audioDir}")
    
    # 检查是否有音频文件
    import glob
    audioFiles = glob.glob(os.path.join(audioDir, "*.wav"))
    
    if not audioFiles:
        print("ℹ️  未找到WAV音频文件")
        print("💡 提示: 请将WAV音频文件放置在audio文件夹中以测试音频功能")
        
        # 创建一个示例说明文件
        readmePath = os.path.join(audioDir, "README.txt")
        with open(readmePath, 'w', encoding='utf-8') as f:
            f.write("请将WAV音频文件放置在此文件夹中\n")
            f.write("支持的格式: WAV (16位PCM)\n")
            f.write("推荐设置: 16kHz采样率, 单声道\n")
        
        print(f"📝 创建说明文件: {readmePath}")
    else:
        print(f"🎵 找到 {len(audioFiles)} 个音频文件:")
        for audioFile in audioFiles:
            print(f"  - {os.path.basename(audioFile)}")

async def DemoProtocol():
    """演示协议功能"""
    print("\n📋 演示协议功能...")
    
    from sk_audio_protocol import SkAudioProtocol, SkMessageHandler
    
    protocol = SkAudioProtocol()
    messageHandler = SkMessageHandler()
    
    # 演示JSON消息创建
    print("📝 创建JSON消息:")
    
    # 创建命令消息
    cmdMsg = protocol.CreateJsonMessage("command", {"cmd": "start_audio"}, 1001)
    print(f"  命令消息: {cmdMsg}")
    
    # 创建状态消息
    statusMsg = messageHandler.CreateWelcomeMessage(1, 1)
    print(f"  欢迎消息: {statusMsg}")
    
    # 演示音频包创建
    print("\n📦 创建音频包:")
    testData = b"test_opus_data"
    audioPacket = protocol.CreateAudioPacket(testData)
    print(f"  音频包长度: {len(audioPacket)} 字节")
    print(f"  头部信息: {audioPacket[:8].hex()}")
    print(f"  负载数据: {audioPacket[8:].hex()}")

async def main():
    """主演示函数"""
    print("SK音频WebSocket服务器功能演示")
    print("基于websocket_audio_server.py的完整实现")
    
    # 创建演示环境
    CreateDemoAudioFiles()
    
    try:
        # 演示协议功能
        await DemoProtocol()
        
        # 启动服务器
        server, serverTask = await DemoServer()
        
        # 演示音频流功能
        await DemoAudioStream(server)
        
        # 演示客户端连接（可选）
        print("\n🔗 客户端连接演示（可选）")
        print("💡 提示: 可以运行 python3 sk_audio_test_client.py 来测试客户端连接")
        
        # 显示服务器状态
        print("\n📊 服务器状态:")
        server.UpdateServerStatus()
        status = server.serverStatus
        
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        print("\n✅ 演示完成!")
        print("🌐 启动Streamlit界面: streamlit run sk_audio_streamlit_app.py")
        print("🧪 启动测试客户端: python3 sk_audio_test_client.py")
        
        # 保持服务器运行一段时间
        print("\n⏰ 服务器将在10秒后自动停止...")
        await asyncio.sleep(10)
        
    except KeyboardInterrupt:
        print("\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
    finally:
        # 清理资源
        if 'server' in locals():
            server.isPlaying = False
            server.audioStreaming = False
        
        if 'serverTask' in locals():
            serverTask.cancel()
            try:
                await serverTask
            except asyncio.CancelledError:
                pass
        
        print("🧹 资源清理完成")

if __name__ == "__main__":
    print("=" * 60)
    print("SK音频WebSocket服务器演示脚本")
    print("=" * 60)
    print("此脚本将演示SK音频WebSocket服务器的各种功能")
    print("包括服务器启动、协议处理、音频流等")
    print("=" * 60)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示结束")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        sys.exit(1)
