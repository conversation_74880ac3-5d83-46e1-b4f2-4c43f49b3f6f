#!/usr/bin/env python3
"""
测试WebSocket服务器命令响应
"""

import asyncio
import websockets
import json
import time

async def test_server_commands():
    """测试服务器命令响应"""
    uri = "ws://192.168.3.17:8768"
    
    print("开始测试服务器命令响应...")
    print("请在服务器端输入命令进行测试")
    print()
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"[{time.strftime('%H:%M:%S')}] 已连接到服务器: {uri}")
            
            # 监听服务器消息
            async for message in websocket:
                if isinstance(message, str):
                    try:
                        data = json.loads(message)
                        msg_type = data.get('type', 'unknown')
                        print(f"[{time.strftime('%H:%M:%S')}] 收到{msg_type}消息: {message}")
                    except json.JSONDecodeError:
                        print(f"[{time.strftime('%H:%M:%S')}] 收到文本消息: {message}")
                elif isinstance(message, bytes):
                    print(f"[{time.strftime('%H:%M:%S')}] 收到二进制消息，长度: {len(message)}字节")
                    
    except websockets.exceptions.ConnectionClosed:
        print(f"[{time.strftime('%H:%M:%S')}] 连接已断开")
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 连接错误: {e}")

if __name__ == "__main__":
    print("WebSocket服务器命令测试客户端")
    print("=" * 50)
    print("此客户端将连接到服务器并监听消息")
    print("请在服务器端输入以下命令进行测试:")
    print("  test    - 测试JSON消息发送")
    print("  start   - 启动音频流")
    print("  stop    - 停止音频流")
    print("  clients - 查看连接的客户端")
    print("  status  - 查看服务器状态")
    print("=" * 50)
    print()
    
    try:
        asyncio.run(test_server_commands())
    except KeyboardInterrupt:
        print("\n测试客户端已停止")
