#!/usr/bin/env python3
"""
SK音频WebSocket服务器 - 简化版Streamlit应用
解决按钮点击无响应问题
"""

import streamlit as st
import asyncio
import websockets
import threading
import time
import json
import os
import glob
import struct
import numpy as np
import logging
from queue import Queue
from typing import Set

# 配置页面
st.set_page_config(
    page_title="SK Audio WebSocket Server",
    page_icon="🎵",
    layout="wide"
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleAudioServer:
    """简化的音频WebSocket服务器"""
    
    def __init__(self, host: str = "localhost", port: int = 8768):
        self.host = host
        self.port = port
        self.connectedClients: Set[websockets.WebSocketServerProtocol] = set()
        self.audioStreaming = False
        self.seqNum = 0
        self.isRunning = False
        self.server = None
        
    async def HandleClient(self, websocket: websockets.WebSocketServerProtocol, path: str):
        """处理客户端连接"""
        clientAddr = websocket.remote_address
        logger.info(f"客户端连接: {clientAddr}")
        self.connectedClients.add(websocket)
        
        try:
            # 发送欢迎消息
            welcomeMsg = {
                "type": "notification",
                "timestamp": int(time.time()),
                "data": {
                    "message": "Welcome to SK Audio WebSocket Server",
                    "connected_clients": len(self.connectedClients)
                }
            }
            await websocket.send(json.dumps(welcomeMsg))
            
            # 处理消息
            async for message in websocket:
                if isinstance(message, str):
                    logger.info(f"收到JSON消息: {message}")
                elif isinstance(message, bytes):
                    logger.info(f"收到二进制消息: {len(message)} 字节")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"客户端断开连接: {clientAddr}")
        except Exception as e:
            logger.error(f"客户端错误: {e}")
        finally:
            self.connectedClients.discard(websocket)
    
    async def SendAudioLoop(self):
        """音频发送循环"""
        while self.isRunning:
            if self.audioStreaming and self.connectedClients:
                # 生成模拟音频数据
                opusData = self.GenerateAudioData()
                packet = self.CreateAudioPacket(opusData)
                
                # 发送给所有客户端
                disconnected = set()
                for websocket in self.connectedClients:
                    try:
                        await websocket.send(packet)
                    except:
                        disconnected.add(websocket)
                
                # 移除断开的客户端
                self.connectedClients -= disconnected
            
            await asyncio.sleep(0.02)  # 20ms间隔
    
    def GenerateAudioData(self) -> bytes:
        """生成模拟音频数据"""
        frameSize = 60
        data = bytearray(frameSize)
        data[0] = 0xFC
        data[1] = frameSize & 0xFF
        for i in range(2, frameSize):
            data[i] = (self.seqNum + i) & 0xFF
        return bytes(data)
    
    def CreateAudioPacket(self, opusData: bytes) -> bytes:
        """创建音频包"""
        header = struct.pack('<BBHHH', 0x01, 0x01, self.seqNum & 0xFFFF, len(opusData), 0x00)
        self.seqNum += 1
        return header + opusData
    
    async def Start(self):
        """启动服务器"""
        try:
            self.isRunning = True
            logger.info(f"启动服务器: {self.host}:{self.port}")
            
            # 启动WebSocket服务器
            self.server = await websockets.serve(self.HandleClient, self.host, self.port)
            
            # 启动音频发送任务
            audioTask = asyncio.create_task(self.SendAudioLoop())
            
            logger.info(f"服务器已启动: ws://{self.host}:{self.port}")
            
            # 等待服务器运行
            await asyncio.Future()
            
        except Exception as e:
            logger.error(f"服务器启动失败: {e}")
            raise e
    
    def Stop(self):
        """停止服务器"""
        self.isRunning = False
        self.audioStreaming = False
        if self.server:
            self.server.close()

# 初始化会话状态
if 'server' not in st.session_state:
    st.session_state.server = None
if 'server_running' not in st.session_state:
    st.session_state.server_running = False
if 'server_error' not in st.session_state:
    st.session_state.server_error = None
if 'audio_playing' not in st.session_state:
    st.session_state.audio_playing = False

def StartServer(host: str, port: int):
    """启动服务器"""
    def run_server():
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            server = SimpleAudioServer(host, port)
            st.session_state.server = server

            # 标记服务器为运行状态
            server.isRunning = True

            loop.run_until_complete(server.Start())
        except OSError as e:
            if "10048" in str(e) or "Address already in use" in str(e):
                st.session_state.server_error = f"端口 {port} 已被占用"
            else:
                st.session_state.server_error = f"服务器启动失败: {e}"
            st.session_state.server_running = False
        except Exception as e:
            st.session_state.server_error = f"服务器错误: {e}"
            st.session_state.server_running = False
        finally:
            # 确保清理状态
            if st.session_state.server:
                st.session_state.server.isRunning = False

    if not st.session_state.server_running:
        st.session_state.server_running = True
        st.session_state.server_error = None
        thread = threading.Thread(target=run_server, daemon=True)
        thread.start()
        return True
    return False

def StopServer():
    """停止服务器"""
    if st.session_state.server:
        st.session_state.server.Stop()
        st.session_state.server = None
    st.session_state.server_running = False
    st.session_state.audio_playing = False
    st.session_state.server_error = None

# 主界面
st.title("🎵 SK Audio WebSocket Server")
st.markdown("**简化版 - 解决按钮响应问题**")

# 状态显示
col1, col2, col3, col4 = st.columns(4)

with col1:
    if st.session_state.server_running and not st.session_state.server_error:
        st.success("🟢 服务器运行中")
    elif st.session_state.server_error:
        st.error("🔴 服务器启动失败")
    else:
        st.error("🔴 服务器未运行")

with col2:
    if st.session_state.server and st.session_state.server.audioStreaming:
        st.success("🎵 音频播放中")
    else:
        st.info("⏸️ 音频已停止")

with col3:
    if st.session_state.server and st.session_state.server.connectedClients:
        client_count = len(st.session_state.server.connectedClients)
        st.info(f"👥 {client_count} 个客户端")
    else:
        st.info("👥 无客户端连接")

with col4:
    if st.button("🔄 刷新状态"):
        # 强制刷新页面状态
        st.experimental_rerun()

# 错误信息和调试信息
if st.session_state.server_error:
    st.error(f"❌ {st.session_state.server_error}")
    if "端口" in st.session_state.server_error:
        st.info("💡 建议：请尝试使用其他端口号")

# 调试信息
with st.expander("🔍 调试信息", expanded=False):
    st.write("**会话状态:**")
    st.write(f"- server_running: {st.session_state.server_running}")
    st.write(f"- server_error: {st.session_state.server_error}")
    st.write(f"- audio_playing: {st.session_state.audio_playing}")

    if st.session_state.server:
        st.write("**服务器对象:**")
        st.write(f"- isRunning: {getattr(st.session_state.server, 'isRunning', 'N/A')}")
        st.write(f"- audioStreaming: {getattr(st.session_state.server, 'audioStreaming', 'N/A')}")
        st.write(f"- connectedClients: {len(getattr(st.session_state.server, 'connectedClients', []))}")
        st.write(f"- seqNum: {getattr(st.session_state.server, 'seqNum', 'N/A')}")
    else:
        st.write("**服务器对象:** None")

    if st.button("🧹 重置状态"):
        # 重置所有状态
        st.session_state.server = None
        st.session_state.server_running = False
        st.session_state.server_error = None
        st.session_state.audio_playing = False
        st.success("状态已重置")
        st.experimental_rerun()

st.divider()

# 控制面板
st.header("🎛️ 控制面板")

# 服务器控制
col1, col2, col3, col4 = st.columns(4)

with col1:
    host = st.text_input("主机地址", value="localhost")

with col2:
    port = st.number_input("端口", min_value=1024, max_value=65535, value=8768)

with col3:
    start_clicked = st.button("🚀 启动服务器", disabled=st.session_state.server_running)

with col4:
    stop_clicked = st.button("🛑 停止服务器", disabled=not st.session_state.server_running)

# 处理服务器控制按钮
if start_clicked:
    if StartServer(host, port):
        st.info("🔄 服务器启动中...")
        time.sleep(2)  # 给服务器更多启动时间
        st.experimental_rerun()

if stop_clicked:
    StopServer()
    st.success("✅ 服务器已停止")
    st.experimental_rerun()

# 检查服务器实际运行状态
if st.session_state.server and st.session_state.server_running:
    # 检查服务器是否真的在运行
    if hasattr(st.session_state.server, 'isRunning'):
        if not st.session_state.server.isRunning and not st.session_state.server_error:
            # 服务器可能已经启动但状态未更新
            st.session_state.server.isRunning = True

st.divider()

# 音频控制
st.subheader("🎵 音频控制")

# 检查服务器是否真正可用
server_available = (
    st.session_state.server is not None and
    st.session_state.server_running and
    not st.session_state.server_error and
    getattr(st.session_state.server, 'isRunning', False)
)

if server_available:
    col1, col2, col3 = st.columns(3)
    
    with col1:
        start_audio_clicked = st.button("▶️ 开始播放", disabled=st.session_state.server.audioStreaming)
    
    with col2:
        stop_audio_clicked = st.button("⏹️ 停止播放", disabled=not st.session_state.server.audioStreaming)
    
    with col3:
        st.metric("音频包序号", st.session_state.server.seqNum)
    
    # 处理音频控制按钮
    if start_audio_clicked:
        st.session_state.server.audioStreaming = True
        st.session_state.audio_playing = True
        st.success("✅ 音频播放已启动")
        st.experimental_rerun()
    
    if stop_audio_clicked:
        st.session_state.server.audioStreaming = False
        st.session_state.audio_playing = False
        st.success("✅ 音频播放已停止")
        st.experimental_rerun()
    
    # 显示当前状态
    if st.session_state.server.audioStreaming:
        st.info("🎵 正在向客户端发送音频数据...")
    else:
        st.info("⏸️ 音频播放已停止")

else:
    st.warning("⚠️ 请先启动服务器")

st.divider()

# JSON消息测试
st.subheader("📤 JSON消息测试")

if server_available:
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("📊 获取状态"):
            st.json({"type": "command", "data": {"cmd": "get_status"}})
    
    with col2:
        if st.button("🎵 开始音频"):
            st.json({"type": "command", "data": {"cmd": "start_audio"}})
    
    with col3:
        if st.button("⏹️ 停止音频"):
            st.json({"type": "command", "data": {"cmd": "stop_audio"}})
    
    with col4:
        if st.button("💓 心跳测试"):
            st.json({"type": "ping", "timestamp": int(time.time())})

else:
    st.warning("⚠️ 请先启动服务器")

# 连接信息
st.divider()
st.subheader("🔗 连接信息")

if st.session_state.server_running and not st.session_state.server_error:
    st.success(f"🌐 服务器地址: ws://{host}:{port}")
    st.info("📱 测试客户端: python3 sk_audio_test_client.py")
else:
    st.warning("⚠️ 服务器未运行")

# 使用说明
with st.expander("📖 使用说明"):
    st.markdown("""
    ### 🚀 使用步骤
    1. 点击"启动服务器"按钮
    2. 点击"开始播放"开始音频流
    3. 使用测试客户端连接: `python3 sk_audio_test_client.py`
    4. 观察客户端连接状态和音频数据传输
    
    ### 🔧 故障排除
    - 如果端口被占用，请更改端口号
    - 确保防火墙允许WebSocket连接
    - 检查客户端连接地址是否正确
    """)

# 减少自动刷新频率
if st.session_state.server_running and not st.session_state.server_error:
    time.sleep(2)
    st.experimental_rerun()
