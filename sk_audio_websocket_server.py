#!/usr/bin/env python3
"""
SK音频WebSocket服务器
基于websocket_audio_server.py的Streamlit版本
"""

import asyncio
import websockets
import json
import time
import threading
import os
import glob
from queue import Queue
from typing import Set, Dict, Any
import logging
from sk_audio_protocol import SkAudioProtocol, SkAudioProcessor, SkMessageHandler

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SkAudioWebSocketServer:
    def __init__(self, host: str = "localhost", port: int = 8768):
        self.host = host
        self.port = port
        self.connectedClients: Set[websockets.WebSocketServerProtocol] = set()
        self.clientCounter = 0
        self.connectionHistory: Dict[str, list] = {}
        self.maxClients = 5
        
        # 音频相关
        self.audioProcessor = SkAudioProcessor()
        self.messageHandler = SkMessageHandler()
        self.audioFilesData = []
        self.currentFileIndex = 0
        self.audioQueue = Queue()
        self.audioThread = None
        self.isPlaying = False
        self.audioStreaming = False
        
        # 统计信息
        self.seqNum = 0
        self.sendSuccBytes = 0
        self.sendFailBytes = 0
        
        # 状态管理
        self.serverStatus = {
            "server_status": "running",
            "connected_clients": 0,
            "audio_files": 0,
            "current_file": None,
            "seq_num": 0,
            "audio_streaming": False,
            "uptime": time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 自动加载音频文件
        self.AutoLoadAudioFiles()
        
    async def HandleClient(self, websocket: websockets.WebSocketServerProtocol, path: str):
        """处理客户端连接"""
        clientAddr = websocket.remote_address
        clientIp = clientAddr[0]
        currentTime = time.time()
        
        # 检查连接数限制
        activeClients = [ws for ws in self.connectedClients if not ws.closed]
        if len(activeClients) >= self.maxClients:
            logger.warning(f"Max clients ({self.maxClients}) reached, rejecting {clientAddr}")
            await websocket.close(code=1013, reason="Server overloaded")
            return
            
        # 检查频繁重连
        if clientIp in self.connectionHistory:
            recentConnections = [t for t in self.connectionHistory[clientIp] if currentTime - t < 10]
            if len(recentConnections) >= 3:
                logger.warning(f"Too many connections from {clientIp}, rejecting...")
                await websocket.close(code=1008, reason="Too many connections")
                return
            self.connectionHistory[clientIp] = recentConnections + [currentTime]
        else:
            self.connectionHistory[clientIp] = [currentTime]
            
        self.clientCounter += 1
        logger.info(f"Client {self.clientCounter} connected: {clientAddr}")
        
        # 添加到连接集合
        self.connectedClients.add(websocket)
        self.UpdateServerStatus()
        
        try:
            # 发送欢迎消息
            welcomeMsg = self.messageHandler.CreateWelcomeMessage(
                self.clientCounter, len(self.connectedClients))
            await websocket.send(welcomeMsg)
            logger.info(f"Sent welcome message to {clientAddr}")
            
            # 创建任务列表
            tasks = []
            
            # 启动消息监听任务
            messageTask = asyncio.create_task(self.HandleMessages(websocket, clientAddr))
            tasks.append(messageTask)
            
            # 如果音频流已启动，则启动音频发送任务
            if self.audioStreaming:
                audioTask = asyncio.create_task(self.SendAudioStream(websocket, clientAddr))
                tasks.append(audioTask)
            
            # 等待任一任务完成
            if tasks:
                done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
                # 取消未完成的任务
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                        
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client disconnected: {clientAddr}")
        except Exception as e:
            logger.error(f"Error with client {clientAddr}: {e}")
        finally:
            self.connectedClients.discard(websocket)
            self.UpdateServerStatus()
            logger.info(f"Client {clientAddr} cleanup completed")
    
    async def HandleMessages(self, websocket: websockets.WebSocketServerProtocol, clientAddr):
        """处理来自客户端的消息"""
        try:
            lastPong = time.time()
            
            async for message in websocket:
                if isinstance(message, str):
                    # 处理文本消息（JSON）
                    await self.HandleTextMessage(websocket, message, clientAddr)
                    lastPong = time.time()
                elif isinstance(message, bytes):
                    # 处理二进制消息
                    logger.info(f"Received binary message from {clientAddr}, length: {len(message)}")
                    lastPong = time.time()
                
                # 检查连接是否超时
                if time.time() - lastPong > 60:
                    logger.warning(f"Client {clientAddr} timeout, closing connection")
                    break
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Message handler disconnected: {clientAddr}")
        except Exception as e:
            logger.error(f"Message handler error with {clientAddr}: {e}")
    
    async def HandleTextMessage(self, websocket: websockets.WebSocketServerProtocol, message: str, clientAddr):
        """处理文本消息（JSON）"""
        try:
            data = json.loads(message)
            logger.info(f"Received JSON from {clientAddr}: {data}")
            
            msgType = data.get('type', '')
            
            if msgType == 'command':
                await self.HandleCommand(websocket, data, clientAddr)
            elif msgType == 'status':
                await self.HandleStatusRequest(websocket, data, clientAddr)
            else:
                logger.warning(f"Unknown message type: {msgType}")
                
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON from {clientAddr}: {e}")
        except Exception as e:
            logger.error(f"Error handling text message from {clientAddr}: {e}")
    
    async def HandleCommand(self, websocket: websockets.WebSocketServerProtocol, data: dict, clientAddr):
        """处理命令消息"""
        cmdResult = self.messageHandler.ProcessCommand(data)
        cmd = cmdResult['cmd']
        
        logger.info(f"Processing command '{cmd}' from {clientAddr}")
        
        # 执行具体命令
        if cmd == "start_audio":
            if not self.audioStreaming:
                self.audioStreaming = True
                if self.audioFilesData:
                    cmdResult['message'] = f"Audio streaming started with {len(self.audioFilesData)} files"
                    # 为所有连接的客户端启动音频发送任务
                    for clientWs in self.connectedClients.copy():
                        if not clientWs.closed:
                            asyncio.create_task(self.SendAudioStream(clientWs, clientWs.remote_address))
                else:
                    cmdResult['message'] = "Audio streaming started with simulated data"
            else:
                cmdResult['message'] = "Audio streaming is already running"
                
        elif cmd == "stop_audio":
            if self.audioStreaming:
                self.audioStreaming = False
                cmdResult['message'] = "Audio streaming stopped"
            else:
                cmdResult['message'] = "Audio streaming is already stopped"
                
        elif cmd == "reload_audio":
            self.ReloadAudioFiles()
            cmdResult['message'] = f"Audio files reloaded, found {len(self.audioFilesData)} files"
        
        # 更新服务器状态
        self.UpdateServerStatus()
        
        # 发送响应
        response = self.messageHandler.CreateCommandResponse(
            cmdResult['seq'], cmd, cmdResult['result'], 
            cmdResult['message'], cmdResult['data'])
        
        await websocket.send(response)
        logger.info(f"Sent command response to {clientAddr}")
    
    async def HandleStatusRequest(self, websocket: websockets.WebSocketServerProtocol, data: dict, clientAddr):
        """处理状态请求"""
        seq = data.get('seq', 0)
        
        response = self.messageHandler.CreateStatusResponse(seq, self.serverStatus)
        await websocket.send(response)
        logger.info(f"Sent status response to {clientAddr}")
    
    async def SendAudioStream(self, websocket: websockets.WebSocketServerProtocol, clientAddr):
        """发送音频流"""
        try:
            while self.audioStreaming:
                # 获取音频数据
                opusData = self.GetAudioData()
                
                if opusData is None:
                    # 没有更多音频数据
                    if self.audioFilesData:
                        logger.info("Audio file finished, restarting...")
                        self.RestartAudio()
                        continue
                    else:
                        # 生成模拟数据
                        opusData = self.audioProcessor.GenerateSimulatedOpusData(self.seqNum)
                
                # 创建WebSocket音频包
                packet = self.messageHandler.protocol.CreateAudioPacket(opusData)
                
                # 发送二进制数据
                await websocket.send(packet)
                
                self.seqNum += 1
                if self.seqNum % 50 == 0:  # 每秒打印一次状态
                    logger.info(f"Sent {self.seqNum} audio packets to {clientAddr}")
                
                # 20ms间隔，模拟实时音频流
                await asyncio.sleep(0.02)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Audio stream disconnected: {clientAddr}")
        except Exception as e:
            logger.error(f"Audio stream error with {clientAddr}: {e}")
    
    async def BroadcastMessage(self, message: dict, exclude: websockets.WebSocketServerProtocol = None):
        """广播消息给所有连接的客户端"""
        if not self.connectedClients:
            return
            
        jsonStr = json.dumps(message)
        disconnected = set()
        
        for websocket in self.connectedClients:
            if websocket == exclude:
                continue
                
            try:
                await websocket.send(jsonStr)
            except websockets.exceptions.ConnectionClosed:
                disconnected.add(websocket)
            except Exception as e:
                logger.error(f"Error broadcasting to client: {e}")
                disconnected.add(websocket)
        
        # 移除断开连接的客户端
        self.connectedClients -= disconnected
        self.UpdateServerStatus()
    
    def AutoLoadAudioFiles(self):
        """自动加载audio文件夹中的音频文件"""
        audioDir = "audio"
        if not os.path.exists(audioDir):
            logger.info(f"音频文件夹 '{audioDir}' 不存在，将使用模拟音频数据")
            return
        
        # 支持的音频文件格式
        audioExtensions = ['*.wav', '*.WAV']
        audioFiles = []
        
        for extension in audioExtensions:
            pattern = os.path.join(audioDir, extension)
            audioFiles.extend(glob.glob(pattern))
        
        if audioFiles:
            logger.info(f"在 '{audioDir}' 文件夹中找到 {len(audioFiles)} 个音频文件:")
            for file in audioFiles:
                logger.info(f"  - {file}")
            
            self.LoadAudioFiles(audioFiles)
        else:
            logger.info(f"在 '{audioDir}' 文件夹中未找到音频文件，将使用模拟音频数据")
    
    def LoadAudioFiles(self, audioFiles: list):
        """加载音频文件"""
        self.audioFilesData = []
        for audioFile in audioFiles:
            audioData = self.audioProcessor.LoadWavFile(audioFile)
            if audioData:
                self.audioFilesData.append(audioData)
        
        if self.audioFilesData:
            self.currentFileIndex = 0
            logger.info(f"已加载 {len(self.audioFilesData)} 个音频文件")
            self.StartAudioThread()
    
    def ReloadAudioFiles(self):
        """重新加载音频文件"""
        logger.info("重新加载音频文件...")
        self.audioFilesData = []
        self.AutoLoadAudioFiles()
    
    def StartAudioThread(self):
        """启动音频处理线程"""
        if self.audioThread and self.audioThread.is_alive():
            return
            
        self.isPlaying = True
        self.audioThread = threading.Thread(target=self.AudioWorker, daemon=True)
        self.audioThread.start()
    
    def AudioWorker(self):
        """音频处理工作线程"""
        frameSize = 320  # 16kHz * 0.02s = 320 samples per 20ms frame
        
        while self.isPlaying and self.audioFilesData:
            currentFileData = self.audioFilesData[self.currentFileIndex]
            audioData = currentFileData['data']
            audioPos = currentFileData['pos']
            
            # 获取一帧音频数据
            frame, newPos = self.audioProcessor.GetAudioFrame(audioData, audioPos, frameSize)
            
            if frame is None:
                # 当前音频文件播放完毕，切换到下一个文件
                logger.info(f"音频文件播放完毕: {currentFileData['file']}")
                self.currentFileIndex = (self.currentFileIndex + 1) % len(self.audioFilesData)
                self.audioFilesData[self.currentFileIndex]['pos'] = 0
                logger.info(f"切换到音频文件: {self.audioFilesData[self.currentFileIndex]['file']}")
                continue
            
            # 更新播放位置
            self.audioFilesData[self.currentFileIndex]['pos'] = newPos
            
            # 使用Opus编码
            opusData = self.audioProcessor.EncodeToOpus(frame)
            
            if opusData:
                # 放入队列
                self.audioQueue.put(opusData)
            
            # 20ms间隔
            time.sleep(0.02)
    
    def GetAudioData(self):
        """获取音频数据"""
        if not self.audioQueue.empty():
            return self.audioQueue.get()
        return None
    
    def RestartAudio(self):
        """重新开始音频播放"""
        if self.audioFilesData:
            self.audioFilesData[self.currentFileIndex]['pos'] = 0
    
    def UpdateServerStatus(self):
        """更新服务器状态"""
        self.serverStatus.update({
            "connected_clients": len(self.connectedClients),
            "audio_files": len(self.audioFilesData),
            "current_file": self.audioFilesData[self.currentFileIndex]['file'] if self.audioFilesData else None,
            "seq_num": self.seqNum,
            "audio_streaming": self.audioStreaming,
            "uptime": time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    async def Start(self):
        """启动WebSocket服务器"""
        logger.info(f"Starting SK Audio WebSocket server on {self.host}:{self.port}")
        
        try:
            async with websockets.serve(self.HandleClient, self.host, self.port):
                logger.info(f"SK Audio WebSocket server started on ws://{self.host}:{self.port}")
                logger.info("Server is ready to accept connections")
                
                # 等待服务器运行
                await asyncio.Future()  # 永远等待
                
        except Exception as e:
            logger.error(f"Server error: {e}")
        finally:
            self.isPlaying = False

# 全局服务器实例
_server_instance = None

def GetServerInstance():
    """获取服务器实例"""
    global _server_instance
    if _server_instance is None:
        _server_instance = SkAudioWebSocketServer()
    return _server_instance

async def StartServer(host: str = "localhost", port: int = 8768):
    """启动服务器"""
    server = SkAudioWebSocketServer(host, port)
    await server.Start()

if __name__ == "__main__":
    import sys
    
    host = "localhost"
    port = 8768
    
    if len(sys.argv) > 1:
        host = sys.argv[1]
    if len(sys.argv) > 2:
        port = int(sys.argv[2])
    
    print("=" * 60)
    print("SK Audio WebSocket Server")
    print("=" * 60)
    print(f"Host: {host}")
    print(f"Port: {port}")
    print("=" * 60)
    
    asyncio.run(StartServer(host, port))
