#!/bin/bash
# Streamlit WebSocket服务器启动脚本

echo "========================================"
echo "Streamlit WebSocket服务器启动脚本"
echo "========================================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查依赖包
echo "📦 检查依赖包..."
pip3 install -r requirements_streamlit.txt

# 启动WebSocket服务器（后台运行）
echo "🚀 启动WebSocket服务器..."
python3 streamlit_websocket_server.py localhost 8765 &
SERVER_PID=$!

# 等待服务器启动
sleep 2

# 检查服务器是否启动成功
if ps -p $SERVER_PID > /dev/null; then
    echo "✅ WebSocket服务器已启动 (PID: $SERVER_PID)"
else
    echo "❌ WebSocket服务器启动失败"
    exit 1
fi

# 启动Streamlit应用
echo "🌐 启动Streamlit应用..."
streamlit run streamlit_app.py --server.port 8501 --server.address localhost

# 清理：当Streamlit应用退出时，停止WebSocket服务器
echo "🛑 停止WebSocket服务器..."
kill $SERVER_PID 2>/dev/null

echo "✅ 服务器已停止"
