#!/bin/bash
"""
WebSocket音频控制服务器启动脚本
作者: AI Assistant
日期: 2025-01-01
"""

# 设置默认参数
DEFAULT_HOST="0.0.0.0"
DEFAULT_PORT="8768"
DEFAULT_AUDIO_DIR="audio"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "WebSocket音频控制服务器启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --host HOST     服务器监听地址 (默认: $DEFAULT_HOST)"
    echo "  -p, --port PORT     服务器监听端口 (默认: $DEFAULT_PORT)"
    echo "  -a, --audio FILE    启动时加载的音频文件"
    echo "  -d, --dir DIR       音频文件目录 (默认: $DEFAULT_AUDIO_DIR)"
    echo "  --help              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认参数启动"
    echo "  $0 -h ************* -p 9000         # 指定IP和端口"
    echo "  $0 -a test.wav                       # 启动时加载音频文件"
    echo "  $0 -d /path/to/audio                 # 指定音频文件目录"
    echo ""
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装或不在PATH中"
        return 1
    fi
    
    print_info "Python版本: $(python3 --version)"
    return 0
}

# 检查Python依赖
check_dependencies() {
    print_info "检查Python依赖..."
    
    local missing_deps=()
    
    # 检查websockets
    if ! python3 -c "import websockets" 2>/dev/null; then
        missing_deps+=("websockets")
    fi
    
    # 检查numpy
    if ! python3 -c "import numpy" 2>/dev/null; then
        missing_deps+=("numpy")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        print_error "缺少以下Python依赖: ${missing_deps[*]}"
        print_info "请运行以下命令安装依赖:"
        print_info "pip3 install ${missing_deps[*]}"
        return 1
    fi
    
    print_success "所有依赖已安装"
    return 0
}

# 检查音频文件
check_audio_files() {
    local audio_dir="$1"
    
    if [ -d "$audio_dir" ]; then
        local wav_count=$(find "$audio_dir" -name "*.wav" | wc -l)
        if [ $wav_count -gt 0 ]; then
            print_success "在 $audio_dir 中找到 $wav_count 个WAV文件"
            find "$audio_dir" -name "*.wav" | head -5 | while read file; do
                print_info "  - $(basename "$file")"
            done
            if [ $wav_count -gt 5 ]; then
                print_info "  ... 还有 $((wav_count - 5)) 个文件"
            fi
        else
            print_warning "在 $audio_dir 中未找到WAV文件"
        fi
    else
        print_warning "音频目录 $audio_dir 不存在"
    fi
}

# 检查服务器脚本
check_server_script() {
    if [ ! -f "audio_control_server.py" ]; then
        print_error "服务器脚本 audio_control_server.py 不存在"
        print_info "请确保在正确的目录中运行此脚本"
        return 1
    fi
    
    print_success "服务器脚本存在"
    return 0
}

# 启动服务器
start_server() {
    local host="$1"
    local port="$2"
    local audio_file="$3"
    
    print_info "启动WebSocket音频控制服务器..."
    print_info "监听地址: $host:$port"
    
    local cmd="python3 audio_control_server.py --host $host --port $port"
    
    if [ -n "$audio_file" ]; then
        if [ -f "$audio_file" ]; then
            print_info "预加载音频文件: $audio_file"
            cmd="$cmd --audio $audio_file"
        else
            print_warning "音频文件 $audio_file 不存在，将使用默认设置"
        fi
    fi
    
    print_info "执行命令: $cmd"
    print_success "服务器启动中..."
    echo ""
    
    # 执行命令
    exec $cmd
}

# 主函数
main() {
    local host="$DEFAULT_HOST"
    local port="$DEFAULT_PORT"
    local audio_file=""
    local audio_dir="$DEFAULT_AUDIO_DIR"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--host)
                host="$2"
                shift 2
                ;;
            -p|--port)
                port="$2"
                shift 2
                ;;
            -a|--audio)
                audio_file="$2"
                shift 2
                ;;
            -d|--dir)
                audio_dir="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示启动信息
    echo "========================================"
    echo "  WebSocket音频控制服务器启动脚本"
    echo "========================================"
    echo ""
    
    # 执行检查
    print_info "执行系统检查..."
    
    if ! check_python; then
        exit 1
    fi
    
    if ! check_dependencies; then
        exit 1
    fi
    
    if ! check_server_script; then
        exit 1
    fi
    
    check_audio_files "$audio_dir"
    
    echo ""
    print_success "系统检查完成"
    echo ""
    
    # 启动服务器
    start_server "$host" "$port" "$audio_file"
}

# 捕获Ctrl+C信号
trap 'print_info "服务器正在关闭..."; exit 0' INT

# 运行主函数
main "$@"
