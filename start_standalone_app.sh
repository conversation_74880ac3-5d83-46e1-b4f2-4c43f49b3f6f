#!/bin/bash
# SK音频WebSocket服务器 - 独立应用启动脚本

echo "========================================"
echo "SK音频WebSocket服务器 - 独立应用"
echo "========================================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查依赖包
echo "📦 检查依赖包..."
pip3 install streamlit websockets numpy

# 检查audio文件夹
if [ ! -d "audio" ]; then
    echo "📁 创建audio文件夹..."
    mkdir audio
    echo "ℹ️  请将WAV音频文件放置在audio文件夹中"
fi

# 启动独立Streamlit应用
echo "🌐 启动独立Streamlit应用..."
echo "📡 应用将在 http://localhost:8501 启动"
echo "🎵 WebSocket服务器将在应用内启动"
echo ""

streamlit run sk_audio_standalone_app.py --server.port 8501 --server.address localhost

echo "✅ 应用已退出"
