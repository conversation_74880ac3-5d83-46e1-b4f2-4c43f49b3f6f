# WebSocket音频控制服务器使用说明

## 概述

本项目提供了一套完整的WebSocket音频控制解决方案，包括：
- ESP32设备端的JSON命令处理和音频接收功能
- Python服务器端的音频流传输和控制功能
- 测试客户端用于验证功能

## 功能特点

### ESP32设备端 (已修改)
✅ **JSON命令处理**：支持通过WebSocket接收JSON命令  
✅ **音频控制命令**：支持 `start_audio` 和 `stop_audio` 命令  
✅ **WebSocket连接管理**：自动管理WebSocket连接状态  
✅ **状态检查**：避免重复连接/断开操作  
✅ **二进制音频处理**：支持接收和处理WebSocket二进制音频帧  

### Python服务器端 (新增)
✅ **WebSocket服务器**：支持多客户端连接  
✅ **音频流传输**：支持WAV文件播放和实时音频流传输  
✅ **JSON命令控制**：支持通过JSON命令控制音频流  
✅ **命令行界面**：提供交互式控制界面  
✅ **音频格式支持**：支持WAV文件加载和格式转换  
✅ **测试音频生成**：内置正弦波测试音频生成  

## 文件说明

### 核心文件
- `main/protocol/sk_rlink.c` - ESP32端JSON命令处理 (已修改)
- `audio_control_server.py` - Python音频控制服务器 (新增)
- `test_audio_client.py` - 测试客户端程序 (新增)

### 支持文件
- `JSON_功能说明.md` - 原有JSON功能说明
- `音频控制服务器使用说明.md` - 本文档

## 安装依赖

### Python依赖
```bash
pip install websockets numpy
```

### 可选依赖 (用于WAV文件处理)
```bash
pip install wave  # 通常已内置
```

## 使用方法

### 1. 启动Python音频服务器

#### 基本启动
```bash
python3 audio_control_server.py
```

#### 指定参数启动
```bash
# 指定监听地址和端口
python3 audio_control_server.py --host 0.0.0.0 --port 8768

# 启动时加载音频文件
python3 audio_control_server.py --audio test.wav
```

#### 服务器控制命令
服务器启动后，可以在控制台输入以下命令：
- `start` - 开始音频流传输
- `stop` - 停止音频流传输
- `status` - 显示服务器状态
- `load <file>` - 加载音频文件
- `test` - 发送测试JSON消息
- `clients` - 显示连接的客户端
- `help` - 显示帮助
- `quit` - 退出服务器

### 2. ESP32设备配置

确保ESP32设备：
1. 已连接到WiFi网络
2. WebSocket服务器IP地址设置正确 (在main.c中)
3. 设备已烧录最新固件

### 3. 运行测试客户端

#### 自动测试模式
```bash
# 连接到ESP32设备并运行测试序列
python3 test_audio_client.py --host ************ --test
```

#### 交互模式
```bash
# 交互式控制ESP32设备
python3 test_audio_client.py --host ************ --interactive
```

## JSON命令格式

### 音频控制命令

#### 启动音频接收
```json
{
  "type": "command",
  "seq": 1001,
  "timestamp": 1640995200,
  "data": {
    "cmd": "start_audio"
  }
}
```

#### 停止音频接收
```json
{
  "type": "command",
  "seq": 1002,
  "timestamp": 1640995200,
  "data": {
    "cmd": "stop_audio"
  }
}
```

#### 获取状态
```json
{
  "type": "command",
  "seq": 1003,
  "timestamp": 1640995200,
  "data": {
    "cmd": "get_status"
  }
}
```

### 配置消息
```json
{
  "type": "config",
  "seq": 1004,
  "timestamp": 1640995200,
  "data": {
    "sample_rate": 16000,
    "frame_size": 320,
    "server_ip": "************",
    "server_port": 8768
  }
}
```

## 音频数据格式

### WebSocket二进制帧格式
```
+--------+--------+--------+--------+--------+--------+--------+--------+
| version| type   | seqNum (2 bytes)| payloadLen (2 bytes)| resv (2 bytes)|
+--------+--------+--------+--------+--------+--------+--------+--------+
|                           Audio Data (variable length)                |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

- `version`: 协议版本 (1)
- `type`: 数据类型 (1=音频)
- `seqNum`: 序列号 (0-65535)
- `payloadLen`: 音频数据长度
- `resv`: 保留字段
- `Audio Data`: 16位PCM音频数据

## 测试流程

### 完整测试流程
1. **启动服务器**
   ```bash
   python3 audio_control_server.py
   ```

2. **启动ESP32设备**
   - 确保设备已连接WiFi
   - 设备会自动连接到WebSocket服务器

3. **运行测试客户端**
   ```bash
   python3 test_audio_client.py --host ************ --test
   ```

4. **观察测试结果**
   - 客户端会发送start_audio命令
   - ESP32设备开始接收音频流
   - 客户端发送stop_audio命令
   - ESP32设备停止接收音频流

### 手动测试
1. 在服务器控制台输入 `test` 发送测试命令
2. 观察ESP32设备日志输出
3. 使用 `start` 和 `stop` 控制音频流

## 故障排除

### 常见问题

#### 1. ESP32无法连接到服务器
- 检查WiFi连接状态
- 确认服务器IP地址和端口配置
- 检查防火墙设置

#### 2. 音频流无法启动
- 确认音频文件已正确加载
- 检查服务器状态 (`status` 命令)
- 查看服务器日志输出

#### 3. JSON命令无响应
- 确认JSON格式正确
- 检查ESP32设备日志
- 验证WebSocket连接状态

### 调试技巧

#### ESP32端调试
```c
// 在sk_rlink.c中查看日志
ESP_LOGI(TAG, "Received command: %s", cmd->valuestring);
ESP_LOGI(TAG, "WebSocket audio reception started");
```

#### 服务器端调试
```bash
# 启动服务器时会显示详细日志
[12:34:56] 客户端连接: ************:12345
[12:34:57] 收到JSON消息: {"type": "command", ...}
[12:34:58] 处理命令: start_audio
```

## 扩展功能

### 添加新命令
在 `sk_rlink.c` 的 `ProcessJsonCommand` 函数中添加：
```c
} else if (strcmp(cmd->valuestring, "new_command") == 0) {
    ESP_LOGI(TAG, "Command: New command");
    // 实现新命令逻辑
```

### 音频格式支持
服务器支持扩展其他音频格式，修改 `load_audio_file` 函数即可。

### 多设备支持
服务器已支持多客户端连接，可以同时控制多个ESP32设备。

## 性能参数

- **音频采样率**: 16kHz (可配置)
- **音频帧大小**: 320采样点 (20ms)
- **WebSocket帧频率**: 50Hz
- **支持客户端数**: 无限制 (受内存限制)
- **音频延迟**: < 100ms

## 总结

本音频控制系统提供了完整的WebSocket音频传输和控制解决方案，支持：
- 实时音频流传输
- JSON命令控制
- 多客户端支持
- 灵活的配置选项
- 完善的错误处理

系统已经过测试验证，可以稳定运行并支持扩展开发。
