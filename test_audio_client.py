#!/usr/bin/env python3
"""
WebSocket音频控制客户端测试程序
用于测试ESP32设备的音频控制功能
作者: AI Assistant
日期: 2025-01-01
"""

import asyncio
import websockets
import json
import time
import struct
import sys

class AudioTestClient:
    def __init__(self, uri):
        self.uri = uri
        self.websocket = None
        self.running = False
        self.audio_frame_count = 0
        self.json_message_count = 0
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(self.uri)
            self.running = True
            print(f"[{time.strftime('%H:%M:%S')}] 已连接到服务器: {self.uri}")
            return True
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            print(f"[{time.strftime('%H:%M:%S')}] 已断开连接")
            
    async def send_json_command(self, cmd, params=None):
        """发送JSON命令"""
        if not self.websocket:
            print("未连接到服务器")
            return
            
        message = {
            "type": "command",
            "seq": int(time.time() * 1000) % 10000,
            "timestamp": int(time.time()),
            "data": {
                "cmd": cmd
            }
        }
        
        if params:
            message["data"]["params"] = params
            
        try:
            await self.websocket.send(json.dumps(message))
            print(f"[{time.strftime('%H:%M:%S')}] 发送命令: {cmd}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 发送命令失败: {e}")
            
    async def send_config(self, config_data):
        """发送配置消息"""
        if not self.websocket:
            print("未连接到服务器")
            return
            
        message = {
            "type": "config",
            "seq": int(time.time() * 1000) % 10000,
            "timestamp": int(time.time()),
            "data": config_data
        }
        
        try:
            await self.websocket.send(json.dumps(message))
            print(f"[{time.strftime('%H:%M:%S')}] 发送配置: {config_data}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 发送配置失败: {e}")
            
    async def listen_messages(self):
        """监听服务器消息"""
        if not self.websocket:
            return
            
        try:
            async for message in self.websocket:
                if isinstance(message, str):
                    # JSON文本消息
                    await self.handle_json_message(message)
                else:
                    # 二进制消息
                    await self.handle_binary_message(message)
        except websockets.exceptions.ConnectionClosed:
            print(f"[{time.strftime('%H:%M:%S')}] 服务器连接已关闭")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 监听消息错误: {e}")
            
    async def handle_json_message(self, message):
        """处理JSON消息"""
        try:
            data = json.loads(message)
            self.json_message_count += 1
            
            msg_type = data.get('type', 'unknown')
            print(f"[{time.strftime('%H:%M:%S')}] 收到JSON消息 #{self.json_message_count}: {msg_type}")
            
            if msg_type == 'status':
                self.print_status_info(data.get('data', {}))
            else:
                print(f"  内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
        except json.JSONDecodeError as e:
            print(f"[{time.strftime('%H:%M:%S')}] JSON解析错误: {e}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 处理JSON消息错误: {e}")
            
    async def handle_binary_message(self, message):
        """处理二进制消息"""
        self.audio_frame_count += 1
        
        if len(message) < 8:
            print(f"[{time.strftime('%H:%M:%S')}] 二进制消息太短: {len(message)} 字节")
            return
            
        try:
            # 解析WebSocket二进制帧头部
            header = struct.unpack('<BBHHH', message[:8])
            version, data_type, seq_num, payload_len, resv = header
            
            if self.audio_frame_count % 50 == 1:  # 每50帧打印一次
                print(f"[{time.strftime('%H:%M:%S')}] 音频帧 #{self.audio_frame_count}:")
                print(f"  版本: {version}, 类型: {data_type}, 序列号: {seq_num}")
                print(f"  负载长度: {payload_len}, 实际长度: {len(message) - 8}")
                
            # 验证数据完整性
            if len(message) != 8 + payload_len:
                print(f"[{time.strftime('%H:%M:%S')}] 数据长度不匹配: 期望 {8 + payload_len}, 实际 {len(message)}")
                
        except struct.error as e:
            print(f"[{time.strftime('%H:%M:%S')}] 解析二进制消息错误: {e}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 处理二进制消息错误: {e}")
            
    def print_status_info(self, status_data):
        """打印状态信息"""
        print(f"  === 服务器状态 ===")
        print(f"  音频流状态: {status_data.get('audio_streaming', 'unknown')}")
        print(f"  连接客户端数: {status_data.get('connected_clients', 'unknown')}")
        print(f"  采样率: {status_data.get('sample_rate', 'unknown')} Hz")
        print(f"  帧大小: {status_data.get('frame_size', 'unknown')} 采样点")
        print(f"  序列号: {status_data.get('seq_num', 'unknown')}")
        print(f"  音频数据: {'已加载' if status_data.get('audio_loaded', False) else '未加载'}")
        if status_data.get('audio_length', 0) > 0:
            duration = status_data['audio_length'] / status_data.get('sample_rate', 16000)
            print(f"  音频长度: {status_data['audio_length']} 采样点 ({duration:.2f}秒)")
            
    async def run_test_sequence(self):
        """运行测试序列"""
        print(f"\n[{time.strftime('%H:%M:%S')}] 开始测试序列...")
        
        # 1. 发送get_status命令
        print(f"\n--- 步骤1: 获取初始状态 ---")
        await self.send_json_command("get_status")
        await asyncio.sleep(2)
        
        # 2. 发送start_audio命令
        print(f"\n--- 步骤2: 启动音频接收 ---")
        await self.send_json_command("start_audio")
        await asyncio.sleep(3)
        
        # 3. 再次获取状态
        print(f"\n--- 步骤3: 获取音频启动后状态 ---")
        await self.send_json_command("get_status")
        await asyncio.sleep(5)  # 等待接收一些音频帧
        
        # 4. 发送stop_audio命令
        print(f"\n--- 步骤4: 停止音频接收 ---")
        await self.send_json_command("stop_audio")
        await asyncio.sleep(2)
        
        # 5. 最终状态检查
        print(f"\n--- 步骤5: 获取最终状态 ---")
        await self.send_json_command("get_status")
        await asyncio.sleep(2)
        
        print(f"\n[{time.strftime('%H:%M:%S')}] 测试序列完成")
        print(f"总计收到 {self.json_message_count} 条JSON消息")
        print(f"总计收到 {self.audio_frame_count} 个音频帧")
        
    async def interactive_mode(self):
        """交互模式"""
        print(f"\n[{time.strftime('%H:%M:%S')}] 进入交互模式")
        print("可用命令:")
        print("  start_audio   - 启动音频接收")
        print("  stop_audio    - 停止音频接收")
        print("  get_status    - 获取状态")
        print("  start_record  - 开始录音")
        print("  stop_record   - 停止录音")
        print("  config        - 发送配置")
        print("  test          - 运行测试序列")
        print("  stats         - 显示统计信息")
        print("  quit          - 退出")
        print()
        
        while self.running:
            try:
                command = input("> ").strip().lower()
                
                if command == "start_audio":
                    await self.send_json_command("start_audio")
                elif command == "stop_audio":
                    await self.send_json_command("stop_audio")
                elif command == "get_status":
                    await self.send_json_command("get_status")
                elif command == "start_record":
                    await self.send_json_command("start_record")
                elif command == "stop_record":
                    await self.send_json_command("stop_record")
                elif command == "config":
                    config = {
                        "sample_rate": 16000,
                        "frame_size": 320,
                        "server_ip": "************",
                        "server_port": 8768
                    }
                    await self.send_config(config)
                elif command == "test":
                    await self.run_test_sequence()
                elif command == "stats":
                    print(f"JSON消息数: {self.json_message_count}")
                    print(f"音频帧数: {self.audio_frame_count}")
                elif command == "quit":
                    break
                elif command == "help":
                    print("可用命令: start_audio, stop_audio, get_status, start_record, stop_record, config, test, stats, quit")
                else:
                    print(f"未知命令: {command}")
                    
                await asyncio.sleep(0.1)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"[{time.strftime('%H:%M:%S')}] 命令处理错误: {e}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='WebSocket音频控制客户端测试程序')
    parser.add_argument('--host', default='************', help='ESP32设备IP地址')
    parser.add_argument('--port', type=int, default=8768, help='WebSocket端口')
    parser.add_argument('--test', action='store_true', help='运行自动测试序列')
    parser.add_argument('--interactive', action='store_true', help='交互模式')
    
    args = parser.parse_args()
    
    uri = f"ws://{args.host}:{args.port}"
    client = AudioTestClient(uri)
    
    # 连接到服务器
    if not await client.connect():
        return
        
    try:
        # 启动消息监听任务
        listen_task = asyncio.create_task(client.listen_messages())
        
        if args.test:
            # 运行自动测试
            await client.run_test_sequence()
        elif args.interactive:
            # 交互模式
            await client.interactive_mode()
        else:
            # 默认运行测试序列
            await client.run_test_sequence()
            
        # 等待一段时间以接收剩余消息
        await asyncio.sleep(2)
        
    except KeyboardInterrupt:
        print(f"\n[{time.strftime('%H:%M:%S')}] 用户中断")
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 客户端错误: {e}")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
