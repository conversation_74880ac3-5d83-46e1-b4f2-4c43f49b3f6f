#!/usr/bin/env python3
"""
WebSocket重连功能测试程序
用于测试ESP32设备的WebSocket自动重连功能
作者: AI Assistant
日期: 2025-01-01
"""

import asyncio
import websockets
import json
import time
import signal
import sys
from websockets.server import serve

class WebSocketReconnectTester:
    def __init__(self, host='0.0.0.0', port=8768):
        self.host = host
        self.port = port
        self.server = None
        self.clients = set()
        self.running = False
        self.connection_count = 0
        self.disconnection_count = 0
        
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        self.connection_count += 1
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        
        print(f"[{time.strftime('%H:%M:%S')}] 客户端连接 #{self.connection_count}: {client_info}")
        self.clients.add(websocket)
        
        try:
            async for message in websocket:
                if isinstance(message, str):
                    print(f"[{time.strftime('%H:%M:%S')}] 收到JSON消息: {message}")
                    # 回复状态消息
                    response = {
                        "type": "status",
                        "timestamp": int(time.time()),
                        "data": {
                            "server_status": "running",
                            "connection_count": self.connection_count,
                            "disconnection_count": self.disconnection_count
                        }
                    }
                    await websocket.send(json.dumps(response))
                else:
                    print(f"[{time.strftime('%H:%M:%S')}] 收到二进制消息: {len(message)} 字节")
                    
        except websockets.exceptions.ConnectionClosed:
            pass
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 客户端处理错误: {e}")
        finally:
            self.disconnection_count += 1
            self.clients.discard(websocket)
            print(f"[{time.strftime('%H:%M:%S')}] 客户端断开 #{self.disconnection_count}: {client_info}")
            
    async def start_server(self):
        """启动服务器"""
        print(f"[{time.strftime('%H:%M:%S')}] 启动WebSocket重连测试服务器")
        print(f"[{time.strftime('%H:%M:%S')}] 监听地址: ws://{self.host}:{self.port}")
        
        self.server = await serve(self.handle_client, self.host, self.port)
        self.running = True
        
        print(f"[{time.strftime('%H:%M:%S')}] 服务器启动成功")
        print(f"[{time.strftime('%H:%M:%S')}] 等待ESP32设备连接...")
        print()
        print("测试命令:")
        print("  Ctrl+C  - 模拟服务器崩溃（测试客户端重连）")
        print("  输入 'r' - 重启服务器")
        print("  输入 'q' - 退出测试")
        print()
        
    async def stop_server(self):
        """停止服务器"""
        if self.server:
            print(f"[{time.strftime('%H:%M:%S')}] 正在关闭服务器...")
            self.server.close()
            await self.server.wait_closed()
            self.running = False
            print(f"[{time.strftime('%H:%M:%S')}] 服务器已关闭")
            
    async def restart_server(self):
        """重启服务器"""
        print(f"[{time.strftime('%H:%M:%S')}] 重启服务器...")
        await self.stop_server()
        await asyncio.sleep(2)  # 等待2秒
        await self.start_server()
        
    def show_statistics(self):
        """显示统计信息"""
        print(f"\n=== 连接统计 ===")
        print(f"总连接次数: {self.connection_count}")
        print(f"总断开次数: {self.disconnection_count}")
        print(f"当前连接数: {len(self.clients)}")
        print(f"服务器状态: {'运行中' if self.running else '已停止'}")
        print()
        
    async def command_interface(self):
        """命令行界面"""
        while True:
            try:
                # 使用asyncio的方式读取输入
                await asyncio.sleep(0.1)
                
                # 简单的命令处理（在实际应用中可能需要更复杂的输入处理）
                # 这里我们使用定时显示统计信息
                if self.connection_count > 0 and self.connection_count % 5 == 0:
                    self.show_statistics()
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"[{time.strftime('%H:%M:%S')}] 命令处理错误: {e}")
                
    async def run_test(self):
        """运行测试"""
        try:
            # 启动服务器
            await self.start_server()
            
            # 运行命令界面
            await self.command_interface()
            
        except KeyboardInterrupt:
            print(f"\n[{time.strftime('%H:%M:%S')}] 收到中断信号，模拟服务器崩溃...")
            await self.stop_server()
            
            # 等待一段时间后重启，测试客户端重连
            print(f"[{time.strftime('%H:%M:%S')}] 等待10秒后重启服务器，测试客户端重连...")
            await asyncio.sleep(10)
            
            print(f"[{time.strftime('%H:%M:%S')}] 重启服务器，观察客户端是否自动重连...")
            await self.start_server()
            
            # 继续运行
            await self.command_interface()
            
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 测试错误: {e}")
        finally:
            await self.stop_server()
            self.show_statistics()


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='WebSocket重连功能测试服务器')
    parser.add_argument('--host', default='0.0.0.0', help='服务器监听地址')
    parser.add_argument('--port', type=int, default=8768, help='服务器监听端口')
    
    args = parser.parse_args()
    
    tester = WebSocketReconnectTester(args.host, args.port)
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print(f"\n[{time.strftime('%H:%M:%S')}] 收到信号 {signum}，正在退出...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await tester.run_test()
    except KeyboardInterrupt:
        print(f"\n[{time.strftime('%H:%M:%S')}] 测试结束")
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 程序错误: {e}")


if __name__ == "__main__":
    print("=" * 60)
    print("  WebSocket自动重连功能测试程序")
    print("=" * 60)
    print()
    print("此程序用于测试ESP32设备的WebSocket自动重连功能")
    print("测试方法:")
    print("1. 启动此测试服务器")
    print("2. 启动ESP32设备，等待连接")
    print("3. 按Ctrl+C模拟服务器崩溃")
    print("4. 观察ESP32设备是否自动重连")
    print("5. 服务器重启后观察连接恢复情况")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n[{time.strftime('%H:%M:%S')}] 程序被用户中断")
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 程序异常: {e}")
