#!/usr/bin/env python3
"""
Streamlit WebSocket客户端应用
提供实时聊天和数据展示界面
"""

import streamlit as st
import asyncio
import websockets
import json
import time
import threading
from queue import Queue
import logging

# 配置页面
st.set_page_config(
    page_title="WebSocket Chat",
    page_icon="💬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketClient:
    def __init__(self):
        self.websocket = None
        self.isConnected = False
        self.messageQueue = Queue()
        self.connectionThread = None
        self.serverUrl = "ws://localhost:8765"
        self.username = "User"
        
    async def Connect(self, serverUrl: str):
        """连接到WebSocket服务器"""
        try:
            self.websocket = await websockets.connect(serverUrl)
            self.isConnected = True
            self.serverUrl = serverUrl
            logger.info(f"Connected to {serverUrl}")
            
            # 启动消息接收循环
            await self.ReceiveMessages()
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            self.isConnected = False
            raise e
    
    async def ReceiveMessages(self):
        """接收消息循环"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                self.messageQueue.put(data)
                logger.info(f"Received: {data}")
        except websockets.exceptions.ConnectionClosed:
            logger.info("Connection closed")
            self.isConnected = False
        except Exception as e:
            logger.error(f"Receive error: {e}")
            self.isConnected = False
    
    async def SendMessage(self, message: dict):
        """发送消息"""
        if self.websocket and self.isConnected:
            try:
                await self.websocket.send(json.dumps(message))
                logger.info(f"Sent: {message}")
            except Exception as e:
                logger.error(f"Send error: {e}")
                self.isConnected = False
    
    async def SendChatMessage(self, username: str, message: str):
        """发送聊天消息"""
        chatMsg = {
            "type": "chat",
            "timestamp": int(time.time()),
            "data": {
                "username": username,
                "message": message
            }
        }
        await self.SendMessage(chatMsg)
    
    async def SendCommand(self, cmd: str, seq: int = None):
        """发送命令"""
        if seq is None:
            seq = int(time.time())
            
        cmdMsg = {
            "type": "command",
            "seq": seq,
            "timestamp": int(time.time()),
            "data": {
                "cmd": cmd
            }
        }
        await self.SendMessage(cmdMsg)
    
    async def SendPing(self):
        """发送ping"""
        pingMsg = {
            "type": "ping",
            "timestamp": int(time.time()),
            "data": {}
        }
        await self.SendMessage(pingMsg)
    
    def Disconnect(self):
        """断开连接"""
        if self.websocket:
            asyncio.create_task(self.websocket.close())
        self.isConnected = False

# 初始化会话状态
if 'client' not in st.session_state:
    st.session_state.client = WebSocketClient()
if 'messages' not in st.session_state:
    st.session_state.messages = []
if 'connected' not in st.session_state:
    st.session_state.connected = False
if 'server_status' not in st.session_state:
    st.session_state.server_status = {}

def ConnectToServer():
    """连接到服务器的线程函数"""
    async def connect():
        try:
            await st.session_state.client.Connect(st.session_state.server_url)
        except Exception as e:
            st.error(f"Connection failed: {e}")
    
    def run_connect():
        asyncio.run(connect())
    
    thread = threading.Thread(target=run_connect, daemon=True)
    thread.start()

def SendChatMessage():
    """发送聊天消息的线程函数"""
    async def send():
        await st.session_state.client.SendChatMessage(
            st.session_state.username,
            st.session_state.chat_input
        )
    
    def run_send():
        asyncio.run(send())
    
    if st.session_state.chat_input.strip():
        thread = threading.Thread(target=run_send, daemon=True)
        thread.start()
        st.session_state.chat_input = ""

def SendCommand(cmd: str):
    """发送命令的线程函数"""
    async def send():
        await st.session_state.client.SendCommand(cmd)
    
    def run_send():
        asyncio.run(send())
    
    thread = threading.Thread(target=run_send, daemon=True)
    thread.start()

# 主界面
st.title("💬 WebSocket实时聊天")

# 侧边栏 - 连接设置
with st.sidebar:
    st.header("🔧 连接设置")
    
    # 服务器URL
    server_url = st.text_input("服务器URL", value="ws://localhost:8765", key="server_url")
    
    # 用户名
    username = st.text_input("用户名", value="User", key="username")
    
    # 连接按钮
    col1, col2 = st.columns(2)
    with col1:
        if st.button("🔗 连接", disabled=st.session_state.connected):
            ConnectToServer()
            st.session_state.connected = True
    
    with col2:
        if st.button("❌ 断开", disabled=not st.session_state.connected):
            st.session_state.client.Disconnect()
            st.session_state.connected = False
    
    # 连接状态
    if st.session_state.connected and st.session_state.client.isConnected:
        st.success("✅ 已连接")
    elif st.session_state.connected:
        st.warning("🔄 连接中...")
    else:
        st.error("❌ 未连接")
    
    st.divider()
    
    # 服务器命令
    st.header("🎛️ 服务器命令")
    
    if st.button("📊 获取状态"):
        SendCommand("get_status")
    
    if st.button("👥 获取用户列表"):
        SendCommand("get_clients")
    
    if st.button("🗑️ 清空历史"):
        SendCommand("clear_history")
    
    if st.button("💓 发送心跳"):
        async def ping():
            await st.session_state.client.SendPing()
        
        def run_ping():
            asyncio.run(ping())
        
        thread = threading.Thread(target=run_ping, daemon=True)
        thread.start()

# 主内容区域
col1, col2 = st.columns([2, 1])

with col1:
    st.header("💬 聊天区域")
    
    # 消息显示区域
    message_container = st.container()
    
    # 处理接收到的消息
    while not st.session_state.client.messageQueue.empty():
        try:
            message = st.session_state.client.messageQueue.get_nowait()
            st.session_state.messages.append(message)
        except:
            break
    
    # 显示消息
    with message_container:
        for msg in st.session_state.messages[-20:]:  # 显示最近20条消息
            msgType = msg.get('type', '')
            timestamp = msg.get('timestamp', 0)
            timeStr = time.strftime('%H:%M:%S', time.localtime(timestamp))
            
            if msgType == 'chat':
                data = msg.get('data', {})
                username = data.get('username', 'Unknown')
                message = data.get('message', '')
                st.chat_message("user").write(f"**{username}** ({timeStr}): {message}")
                
            elif msgType == 'welcome':
                data = msg.get('data', {})
                st.info(f"🎉 {data.get('message', 'Welcome!')}")
                
            elif msgType == 'notification':
                data = msg.get('data', {})
                st.info(f"📢 {data.get('message', 'Notification')}")
                
            elif msgType == 'system':
                data = msg.get('data', {})
                st.success(f"🔧 {data.get('message', 'System message')}")
                
            elif msgType == 'response':
                result = msg.get('result', 'unknown')
                message = msg.get('message', '')
                if result == 'success':
                    st.success(f"✅ {message}")
                else:
                    st.error(f"❌ {message}")
                
                # 更新服务器状态
                if msg.get('data'):
                    st.session_state.server_status.update(msg['data'])
    
    # 消息输入
    st.divider()
    chat_input = st.text_input("输入消息...", key="chat_input", on_change=SendChatMessage)
    
    if st.button("📤 发送"):
        SendChatMessage()

with col2:
    st.header("📊 服务器状态")
    
    if st.session_state.server_status:
        status = st.session_state.server_status
        
        st.metric("连接的客户端", status.get('connected_clients', 0))
        st.metric("消息历史数量", status.get('message_history_count', 0))
        
        if 'server_status' in status:
            st.success(f"服务器状态: {status['server_status']}")
        
        if 'uptime' in status:
            st.info(f"运行时间: {status['uptime']}")
        
        if 'clients' in status:
            st.subheader("在线用户")
            for client in status['clients']:
                st.text(f"👤 {client}")
    else:
        st.info("点击'获取状态'按钮获取服务器信息")
    
    st.divider()
    
    # 消息统计
    st.header("📈 消息统计")
    st.metric("本地消息数", len(st.session_state.messages))
    
    # 消息类型统计
    msgTypes = {}
    for msg in st.session_state.messages:
        msgType = msg.get('type', 'unknown')
        msgTypes[msgType] = msgTypes.get(msgType, 0) + 1
    
    if msgTypes:
        st.subheader("消息类型分布")
        for msgType, count in msgTypes.items():
            st.text(f"{msgType}: {count}")

# 自动刷新
time.sleep(0.1)
st.rerun()
