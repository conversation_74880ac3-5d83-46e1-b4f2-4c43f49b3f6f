# SK音频WebSocket服务器使用说明

## 项目概述

这是一个基于原有`websocket_audio_server.py`功能的Streamlit版本，提供了完全相同的WebSocket音频服务器功能，但通过Web界面进行控制和监控。

## 文件结构

```
sk-terminal_1/
├── sk_audio_protocol.py          # 协议处理模块
├── sk_audio_websocket_server.py  # WebSocket服务器
├── sk_audio_streamlit_app.py     # Streamlit控制界面
├── sk_audio_test_client.py       # 测试客户端
├── start_sk_audio_server.sh      # 启动脚本
├── requirements_streamlit.txt     # 依赖包列表
└── audio/                         # 音频文件目录
    ├── audio1.wav
    └── audio2.wav
```

## 功能特性

### 🎵 音频处理功能
- ✅ 支持WAV文件加载和播放
- ✅ 自动重采样到16kHz单声道
- ✅ Opus音频编码（如果可用）
- ✅ 伪Opus编码（备用方案）
- ✅ 20ms音频帧实时发送
- ✅ 多音频文件循环播放

### 🌐 WebSocket服务器功能
- ✅ 多客户端连接管理
- ✅ 连接保护机制（防频繁重连）
- ✅ 最大连接数限制
- ✅ 二进制音频数据传输
- ✅ JSON命令消息处理
- ✅ 实时状态监控

### 📱 Streamlit界面功能
- ✅ 服务器启动/停止控制
- ✅ 音频流开始/停止控制
- ✅ 实时服务器状态显示
- ✅ 连接客户端监控
- ✅ 音频文件管理
- ✅ 测试命令发送
- ✅ 协议信息展示

## 安装和启动

### 1. 安装依赖
```bash
pip install -r requirements_streamlit.txt
```

### 2. 准备音频文件
```bash
mkdir audio
# 将WAV音频文件放置在audio文件夹中
```

### 3. 启动方式

#### 方式一：独立应用启动（推荐）
```bash
chmod +x start_standalone_app.sh
./start_standalone_app.sh
```
或者直接运行：
```bash
streamlit run sk_audio_standalone_app.py
```

#### 方式二：分离式启动
```bash
# 启动WebSocket服务器
python3 sk_audio_websocket_server.py localhost 8768 &

# 启动Streamlit界面
streamlit run sk_audio_streamlit_app.py
```

#### 方式三：仅启动WebSocket服务器
```bash
python3 sk_audio_websocket_server.py [host] [port]
```

### 4. 解决端口占用问题
如果遇到"端口已被占用"错误：
```bash
# 查看占用端口的进程
netstat -ano | findstr :8768

# 或者在应用中更改端口号
# 推荐使用 8769, 8770 等其他端口
```

## 使用方法

### 1. 访问Streamlit界面
- 打开浏览器访问：http://localhost:8501
- 界面包含服务器控制、音频控制、状态监控等功能

### 2. 服务器控制
- **启动服务器**：点击"启动服务器"按钮
- **停止服务器**：点击"停止服务器"按钮
- **配置地址**：修改主机地址和端口号

### 3. 音频控制
- **开始音频流**：向连接的客户端发送音频数据
- **停止音频流**：停止音频数据发送
- **重新加载**：重新扫描audio文件夹中的音频文件

### 4. 状态监控
- **连接客户端**：实时显示连接的客户端数量和信息
- **音频文件**：显示已加载的音频文件数量
- **序列号**：显示当前音频包序列号
- **流状态**：显示音频流是否正在运行

## 测试客户端使用

### 启动测试客户端
```bash
python3 sk_audio_test_client.py [server_url]
# 默认连接到: ws://localhost:8768
```

### 测试客户端命令
- `start` - 发送start_audio命令
- `stop` - 发送stop_audio命令
- `reload` - 发送reload_audio命令
- `status` - 获取服务器状态
- `ping` - 发送心跳测试
- `help` - 显示帮助信息
- `quit` - 断开连接并退出

## 协议格式

### WebSocket音频包格式
```c
struct AudioPacket {
    uint8_t version;     // 版本号 (1)
    uint8_t type;        // 类型 (1)
    uint16_t seqNum;     // 序列号
    uint16_t payloadLen; // 负载长度
    uint16_t reserved;   // 保留字段
    uint8_t data[];      // Opus音频数据
}
```

### JSON命令格式
```json
{
    "type": "command",
    "seq": 1001,
    "timestamp": 1234567890,
    "data": {
        "cmd": "start_audio"
    }
}
```

### 支持的命令
- `start_audio` - 开始音频流
- `stop_audio` - 停止音频流
- `reload_audio` - 重新加载音频文件
- `get_status` - 获取服务器状态

## 与原版功能对比

| 功能 | 原版websocket_audio_server.py | SK音频WebSocket服务器 |
|------|-------------------------------|---------------------|
| WebSocket服务器 | ✅ | ✅ |
| 音频文件加载 | ✅ | ✅ |
| Opus编码 | ✅ | ✅ |
| 多客户端连接 | ✅ | ✅ |
| JSON命令处理 | ✅ | ✅ |
| 连接保护 | ✅ | ✅ |
| 命令行控制 | ✅ | ❌ |
| Web界面控制 | ❌ | ✅ |
| 实时状态监控 | ❌ | ✅ |
| 音频文件管理 | ❌ | ✅ |
| 协议信息展示 | ❌ | ✅ |

## 配置选项

### 服务器配置
- **host**: 服务器监听地址（默认：localhost）
- **port**: 服务器端口（默认：8768）
- **maxClients**: 最大客户端连接数（默认：5）

### Streamlit配置
- **server.port**: Streamlit端口（默认：8501）
- **server.address**: Streamlit地址（默认：localhost）

## 故障排除

### 1. 服务器启动失败
- 检查端口是否被占用
- 确认Python环境和依赖包安装正确
- 查看终端错误信息

### 2. 音频文件无法加载
- 确认audio文件夹存在
- 检查WAV文件格式是否正确
- 查看服务器日志信息

### 3. 客户端连接失败
- 确认服务器已启动
- 检查防火墙设置
- 验证服务器地址和端口

### 4. Opus编码不可用
```bash
# 安装Opus编码库
pip install opuslib
```

## 开发和扩展

### 添加新的命令
1. 在`sk_audio_protocol.py`的`ProcessCommand`方法中添加命令处理
2. 在`sk_audio_websocket_server.py`的`HandleCommand`方法中实现具体逻辑
3. 在Streamlit界面中添加对应的控制按钮

### 自定义音频处理
1. 修改`sk_audio_protocol.py`中的`SkAudioProcessor`类
2. 添加新的音频格式支持
3. 实现自定义编码算法

### 扩展协议格式
1. 在`sk_audio_protocol.py`中定义新的消息格式
2. 更新客户端和服务器的解析逻辑
3. 在Streamlit界面中显示新的协议信息

## 性能优化

### 音频处理优化
- 使用更高效的重采样算法
- 实现音频缓存机制
- 优化Opus编码参数

### 网络传输优化
- 实现自适应码率控制
- 添加网络拥塞检测
- 优化WebSocket缓冲区大小

### 内存使用优化
- 实现音频数据流式处理
- 优化内存分配策略
- 添加内存使用监控

## 安全注意事项

1. **生产环境部署**：
   - 使用HTTPS/WSS协议
   - 添加身份验证机制
   - 限制访问IP范围

2. **输入验证**：
   - 验证音频文件格式
   - 限制文件大小
   - 过滤恶意命令

3. **资源保护**：
   - 限制连接数和频率
   - 监控CPU和内存使用
   - 实现优雅降级机制

## 版本历史

- **v1.0.0** - 初始版本
  - 实现基本WebSocket音频服务器功能
  - 提供Streamlit控制界面
  - 支持WAV文件播放和Opus编码
  - 包含完整的测试客户端

## 技术支持

如有问题或建议，请查看：
1. 服务器日志信息
2. Streamlit界面状态显示
3. 测试客户端连接结果
4. 项目文档和代码注释
