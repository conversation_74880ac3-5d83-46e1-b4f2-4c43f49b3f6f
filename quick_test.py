#!/usr/bin/env python3
"""
快速测试脚本 - 验证音频控制功能
作者: AI Assistant
日期: 2025-01-01
"""

import asyncio
import websockets
import json
import time
import sys
import argparse

async def quick_test(host, port):
    """快速测试ESP32音频控制功能"""
    uri = f"ws://{host}:{port}"
    
    print(f"[{time.strftime('%H:%M:%S')}] 连接到 {uri}")
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"[{time.strftime('%H:%M:%S')}] 连接成功!")
            
            # 测试序列
            tests = [
                ("get_status", "获取初始状态"),
                ("start_audio", "启动音频接收"),
                ("get_status", "检查音频启动状态"),
                ("stop_audio", "停止音频接收"),
                ("get_status", "检查音频停止状态")
            ]
            
            for i, (cmd, desc) in enumerate(tests, 1):
                print(f"\n--- 测试 {i}/5: {desc} ---")
                
                # 发送命令
                message = {
                    "type": "command",
                    "seq": 1000 + i,
                    "timestamp": int(time.time()),
                    "data": {"cmd": cmd}
                }
                
                await websocket.send(json.dumps(message))
                print(f"[{time.strftime('%H:%M:%S')}] 发送命令: {cmd}")
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    if isinstance(response, str):
                        print(f"[{time.strftime('%H:%M:%S')}] 收到响应: {response[:100]}...")
                    else:
                        print(f"[{time.strftime('%H:%M:%S')}] 收到二进制数据: {len(response)} 字节")
                except asyncio.TimeoutError:
                    print(f"[{time.strftime('%H:%M:%S')}] 等待响应超时")
                
                # 测试间隔
                if i < len(tests):
                    await asyncio.sleep(2)
            
            print(f"\n[{time.strftime('%H:%M:%S')}] 测试完成!")
            
    except websockets.exceptions.ConnectionRefused:
        print(f"[{time.strftime('%H:%M:%S')}] 连接被拒绝 - 请检查ESP32设备是否在线")
        return False
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 测试失败: {e}")
        return False
    
    return True

async def ping_test(host, port):
    """简单的连接测试"""
    uri = f"ws://{host}:{port}"
    
    print(f"[{time.strftime('%H:%M:%S')}] Ping测试: {uri}")
    
    try:
        async with websockets.connect(uri, timeout=5) as websocket:
            print(f"[{time.strftime('%H:%M:%S')}] 连接成功! ESP32设备在线")
            
            # 发送简单的状态查询
            message = {
                "type": "command",
                "seq": 999,
                "timestamp": int(time.time()),
                "data": {"cmd": "get_status"}
            }
            
            await websocket.send(json.dumps(message))
            print(f"[{time.strftime('%H:%M:%S')}] 发送状态查询...")
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                print(f"[{time.strftime('%H:%M:%S')}] 设备响应正常")
                return True
            except asyncio.TimeoutError:
                print(f"[{time.strftime('%H:%M:%S')}] 设备无响应")
                return False
                
    except websockets.exceptions.ConnectionRefused:
        print(f"[{time.strftime('%H:%M:%S')}] 连接被拒绝 - ESP32设备离线或地址错误")
        return False
    except asyncio.TimeoutError:
        print(f"[{time.strftime('%H:%M:%S')}] 连接超时 - 请检查网络连接")
        return False
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 连接错误: {e}")
        return False

def print_device_info():
    """打印设备信息和使用说明"""
    print("=" * 50)
    print("  ESP32音频控制设备快速测试")
    print("=" * 50)
    print()
    print("此脚本用于快速验证ESP32设备的音频控制功能")
    print()
    print("测试内容:")
    print("  1. WebSocket连接测试")
    print("  2. JSON命令处理测试")
    print("  3. 音频控制功能测试")
    print()
    print("支持的命令:")
    print("  - get_status  : 获取设备状态")
    print("  - start_audio : 启动音频接收")
    print("  - stop_audio  : 停止音频接收")
    print()

async def main():
    parser = argparse.ArgumentParser(description='ESP32音频控制快速测试')
    parser.add_argument('--host', default='************', help='ESP32设备IP地址')
    parser.add_argument('--port', type=int, default=8768, help='WebSocket端口')
    parser.add_argument('--ping', action='store_true', help='仅执行连接测试')
    parser.add_argument('--quiet', action='store_true', help='静默模式')
    
    args = parser.parse_args()
    
    if not args.quiet:
        print_device_info()
    
    print(f"目标设备: {args.host}:{args.port}")
    print()
    
    if args.ping:
        # 仅执行ping测试
        success = await ping_test(args.host, args.port)
    else:
        # 执行完整测试
        success = await quick_test(args.host, args.port)
    
    if success:
        print(f"\n✅ 测试通过 - ESP32设备功能正常")
        sys.exit(0)
    else:
        print(f"\n❌ 测试失败 - 请检查设备状态")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n[{time.strftime('%H:%M:%S')}] 测试被用户中断")
        sys.exit(1)
