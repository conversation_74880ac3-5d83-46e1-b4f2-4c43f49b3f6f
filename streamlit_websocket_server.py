#!/usr/bin/env python3
"""
Streamlit WebSocket服务器
支持实时消息传输、广播和命令处理
"""

import asyncio
import websockets
import json
import time
import threading
from queue import Queue
from typing import Set, Dict, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StreamlitWebSocketServer:
    def __init__(self, host: str = "localhost", port: int = 8765):
        self.host = host
        self.port = port
        self.connectedClients: Set[websockets.WebSocketServerProtocol] = set()
        self.messageQueue = Queue()
        self.isRunning = False
        self.messageHistory: list = []
        self.maxHistorySize = 100
        self.clientCounter = 0
        self.connectionHistory: Dict[str, list] = {}
        self.maxClients = 10
        
    async def HandleClient(self, websocket: websockets.WebSocketServerProtocol, path: str):
        """处理客户端连接"""
        clientAddr = websocket.remote_address
        clientIp = clientAddr[0]
        currentTime = time.time()
        
        # 检查连接数限制
        activeClients = [ws for ws in self.connectedClients if not ws.closed]
        if len(activeClients) >= self.maxClients:
            logger.warning(f"Max clients ({self.maxClients}) reached, rejecting {clientAddr}")
            await websocket.close(code=1013, reason="Server overloaded")
            return
            
        # 检查频繁重连
        if clientIp in self.connectionHistory:
            recentConnections = [t for t in self.connectionHistory[clientIp] if currentTime - t < 10]
            if len(recentConnections) >= 3:
                logger.warning(f"Too many connections from {clientIp}, rejecting...")
                await websocket.close(code=1008, reason="Too many connections")
                return
            self.connectionHistory[clientIp] = recentConnections + [currentTime]
        else:
            self.connectionHistory[clientIp] = [currentTime]
            
        self.clientCounter += 1
        logger.info(f"Client {self.clientCounter} connected: {clientAddr}")
        
        # 添加到连接集合
        self.connectedClients.add(websocket)
        
        try:
            # 发送欢迎消息
            welcomeMsg = {
                "type": "welcome",
                "timestamp": int(time.time()),
                "data": {
                    "message": "Welcome to Streamlit WebSocket Server",
                    "server_time": time.strftime('%Y-%m-%d %H:%M:%S'),
                    "client_id": self.clientCounter,
                    "connected_clients": len(self.connectedClients)
                }
            }
            await websocket.send(json.dumps(welcomeMsg))
            
            # 发送历史消息
            if self.messageHistory:
                historyMsg = {
                    "type": "history",
                    "timestamp": int(time.time()),
                    "data": {
                        "messages": self.messageHistory[-10:]  # 最近10条消息
                    }
                }
                await websocket.send(json.dumps(historyMsg))
            
            # 广播新用户加入
            await self.BroadcastMessage({
                "type": "notification",
                "timestamp": int(time.time()),
                "data": {
                    "event": "user_joined",
                    "message": f"User {self.clientCounter} joined the chat",
                    "connected_clients": len(self.connectedClients)
                }
            }, exclude=websocket)
            
            # 处理消息
            async for message in websocket:
                await self.HandleMessage(websocket, message, clientAddr)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client disconnected: {clientAddr}")
        except Exception as e:
            logger.error(f"Error with client {clientAddr}: {e}")
        finally:
            self.connectedClients.discard(websocket)
            # 广播用户离开
            await self.BroadcastMessage({
                "type": "notification",
                "timestamp": int(time.time()),
                "data": {
                    "event": "user_left",
                    "message": f"A user left the chat",
                    "connected_clients": len(self.connectedClients)
                }
            })
            logger.info(f"Client {clientAddr} cleanup completed")
    
    async def HandleMessage(self, websocket: websockets.WebSocketServerProtocol, message: str, clientAddr):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            logger.info(f"Received message from {clientAddr}: {data}")
            
            msgType = data.get('type', '')
            
            if msgType == 'chat':
                await self.HandleChatMessage(websocket, data, clientAddr)
            elif msgType == 'command':
                await self.HandleCommand(websocket, data, clientAddr)
            elif msgType == 'ping':
                await self.HandlePing(websocket, data, clientAddr)
            else:
                logger.warning(f"Unknown message type: {msgType}")
                
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON from {clientAddr}: {e}")
        except Exception as e:
            logger.error(f"Error handling message from {clientAddr}: {e}")
    
    async def HandleChatMessage(self, websocket: websockets.WebSocketServerProtocol, data: dict, clientAddr):
        """处理聊天消息"""
        chatData = data.get('data', {})
        username = chatData.get('username', f'User_{clientAddr[1]}')
        message = chatData.get('message', '')
        
        if not message.strip():
            return
            
        # 创建聊天消息
        chatMsg = {
            "type": "chat",
            "timestamp": int(time.time()),
            "data": {
                "username": username,
                "message": message,
                "time": time.strftime('%H:%M:%S'),
                "client_addr": f"{clientAddr[0]}:{clientAddr[1]}"
            }
        }
        
        # 添加到历史记录
        self.messageHistory.append(chatMsg)
        if len(self.messageHistory) > self.maxHistorySize:
            self.messageHistory.pop(0)
        
        # 广播消息
        await self.BroadcastMessage(chatMsg)
        
    async def HandleCommand(self, websocket: websockets.WebSocketServerProtocol, data: dict, clientAddr):
        """处理命令消息"""
        cmdData = data.get('data', {})
        cmd = cmdData.get('cmd', '')
        seq = data.get('seq', 0)
        
        logger.info(f"Processing command '{cmd}' from {clientAddr}")
        
        result = "success"
        message = f"Command '{cmd}' processed successfully"
        responseData = {}
        
        if cmd == "get_status":
            responseData = {
                "server_status": "running",
                "connected_clients": len(self.connectedClients),
                "message_history_count": len(self.messageHistory),
                "uptime": time.strftime('%Y-%m-%d %H:%M:%S')
            }
        elif cmd == "clear_history":
            self.messageHistory.clear()
            message = "Message history cleared"
        elif cmd == "get_clients":
            activeClients = [f"{ws.remote_address[0]}:{ws.remote_address[1]}" 
                           for ws in self.connectedClients if not ws.closed]
            responseData = {
                "clients": activeClients,
                "count": len(activeClients)
            }
        else:
            result = "error"
            message = f"Unknown command: {cmd}"
        
        # 发送响应
        response = {
            "type": "response",
            "seq": seq,
            "timestamp": int(time.time()),
            "result": result,
            "message": message,
            "data": responseData
        }
        
        await websocket.send(json.dumps(response))
        
    async def HandlePing(self, websocket: websockets.WebSocketServerProtocol, data: dict, clientAddr):
        """处理ping消息"""
        pongMsg = {
            "type": "pong",
            "timestamp": int(time.time()),
            "data": {
                "server_time": time.strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        await websocket.send(json.dumps(pongMsg))
    
    async def BroadcastMessage(self, message: dict, exclude: websockets.WebSocketServerProtocol = None):
        """广播消息给所有连接的客户端"""
        if not self.connectedClients:
            return
            
        jsonStr = json.dumps(message)
        disconnected = set()
        
        for websocket in self.connectedClients:
            if websocket == exclude:
                continue
                
            try:
                await websocket.send(jsonStr)
            except websockets.exceptions.ConnectionClosed:
                disconnected.add(websocket)
            except Exception as e:
                logger.error(f"Error broadcasting to client: {e}")
                disconnected.add(websocket)
        
        # 移除断开连接的客户端
        self.connectedClients -= disconnected
    
    async def SendPeriodicMessages(self):
        """定期发送系统消息"""
        while self.isRunning:
            await asyncio.sleep(30)  # 每30秒发送一次
            
            if self.connectedClients:
                statusMsg = {
                    "type": "system",
                    "timestamp": int(time.time()),
                    "data": {
                        "message": "Server heartbeat",
                        "connected_clients": len(self.connectedClients),
                        "server_time": time.strftime('%H:%M:%S')
                    }
                }
                await self.BroadcastMessage(statusMsg)
    
    async def Start(self):
        """启动WebSocket服务器"""
        self.isRunning = True
        logger.info(f"Starting WebSocket server on {self.host}:{self.port}")
        
        try:
            # 启动定期消息任务
            periodicTask = asyncio.create_task(self.SendPeriodicMessages())
            
            # 启动WebSocket服务器
            async with websockets.serve(self.HandleClient, self.host, self.port):
                logger.info(f"WebSocket server started on ws://{self.host}:{self.port}")
                logger.info("Press Ctrl+C to stop the server")
                
                # 等待服务器运行
                await asyncio.Future()  # 永远等待
                
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
        except Exception as e:
            logger.error(f"Server error: {e}")
        finally:
            self.isRunning = False
            if 'periodicTask' in locals():
                periodicTask.cancel()

async def main():
    """主函数"""
    import sys
    
    # 解析命令行参数
    host = "localhost"
    port = 8765
    
    if len(sys.argv) > 1:
        host = sys.argv[1]
    if len(sys.argv) > 2:
        port = int(sys.argv[2])
    
    # 创建并启动服务器
    server = StreamlitWebSocketServer(host, port)
    await server.Start()

if __name__ == "__main__":
    print("=" * 60)
    print("Streamlit WebSocket Server")
    print("=" * 60)
    print("Usage: python streamlit_websocket_server.py [host] [port]")
    print("Default: localhost 8765")
    print("=" * 60)
    
    asyncio.run(main())
